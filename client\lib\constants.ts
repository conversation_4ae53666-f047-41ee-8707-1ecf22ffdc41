// Application constants
export const APP_CONFIG = {
  name: "KostHub",
  description: "Platform Pencarian Kost Inovatif",
  url: "https://kosthub.com",
  version: "1.0.0"
} as const

// API endpoints (for future implementation)
export const API_ENDPOINTS = {
  kosts: "/api/kosts",
  search: "/api/search",
  favorites: "/api/favorites",
  comparison: "/api/comparison",
  auth: "/api/auth"
} as const

// Pagination settings
export const PAGINATION = {
  itemsPerPage: 6,
  maxItemsPerPage: 24
} as const

// Search filters
export const SEARCH_FILTERS = {
  priceRange: {
    min: 300000,
    max: 10000000,
    step: 100000,
    default: [500000, 5000000] as [number, number]
  },
  types: ["semua", "putra", "putri", "campur"] as const,
  sortOptions: [
    { value: "relevance", label: "Paling Relevan" },
    { value: "price-low", label: "Harga Terendah" },
    { value: "price-high", label: "Harga Tertinggi" },
    { value: "rating", label: "Rating Tertinggi" },
    { value: "newest", label: "Terbaru" }
  ]
} as const

// Available facilities
export const FACILITIES = [
  "WiFi",
  "Parkir",
  "Dapur",
  "Listrik",
  "Air",
  "Keamanan",
  "Ruang Tamu",
  "AC",
  "Kasur",
  "Lemari"
] as const

// Popular locations
export const LOCATIONS = [
  "Jakarta Pusat",
  "Jakarta Selatan", 
  "Jakarta Barat",
  "Jakarta Utara",
  "Jakarta Timur",
  "Bandung",
  "Surabaya",
  "Yogyakarta",
  "Semarang",
  "Malang"
] as const

// Comparison limits
export const COMPARISON = {
  maxItems: 3,
  minItems: 2
} as const

// Image settings
export const IMAGES = {
  placeholder: "/placeholder-kost.jpg",
  quality: 80,
  sizes: {
    thumbnail: "300x200",
    card: "400x300",
    preview: "800x600",
    hero: "1200x800"
  }
} as const

// Animation durations (in ms)
export const ANIMATIONS = {
  fast: 150,
  normal: 300,
  slow: 500
} as const

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  "2xl": 1536
} as const

// Local storage keys
export const STORAGE_KEYS = {
  favorites: "kosthub_favorites",
  searchHistory: "kosthub_search_history",
  comparison: "kosthub_comparison",
  preferences: "kosthub_preferences"
} as const
