"use client"

import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Footer, CardHeader } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { 
  MapPin, 
  Wifi, 
  Car, 
  Utensils, 
  Zap, 
  Droplets, 
  Shield, 
  Users,
  Heart,
  Eye,
  Star
} from "lucide-react"
import { cn } from "@/lib/utils"
import { formatPrice } from "@/lib/format"

export interface KostData {
  id: string
  title: string
  location: string
  price: number
  rating: number
  reviewCount: number
  images: string[]
  facilities: string[]
  type: "putra" | "putri" | "campur"
  available: number
  description: string
  isWishlisted?: boolean
}

interface KostCardProps {
  kost: KostData
  onPreview?: (kost: KostData) => void
  onWishlist?: (kostId: string) => void
  onCompare?: (kostId: string) => void
  isComparing?: boolean
  className?: string
}

const facilityIcons: Record<string, React.ComponentType<any>> = {
  wifi: Wifi,
  parkir: Car,
  dapur: Utensils,
  listrik: Zap,
  air: Droplets,
  keamanan: Shield,
  "ruang tamu": Users,
}

export function KostCard({ 
  kost, 
  onPreview, 
  onWishlist, 
  onCompare,
  isComparing = false,
  className 
}: KostCardProps) {


  const getTypeColor = (type: string) => {
    switch (type) {
      case "putra":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      case "putri":
        return "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300"
      case "campur":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  return (
    <Card className={cn("kost-card group overflow-hidden", className)}>
      <CardHeader className="p-0 relative">
        <div className="relative aspect-[4/3] overflow-hidden">
          <Image
            src={kost.images[0] || "/placeholder-kost.jpg"}
            alt={kost.title}
            fill
            className="kost-card-image object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          
          {/* Overlay Actions */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300">
            <div className="absolute top-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <Button
                size="sm"
                variant="secondary"
                className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                onClick={() => onWishlist?.(kost.id)}
              >
                <Heart 
                  className={cn(
                    "h-4 w-4",
                    kost.isWishlisted ? "fill-red-500 text-red-500" : "text-gray-600"
                  )} 
                />
              </Button>
              <Button
                size="sm"
                variant="secondary"
                className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                onClick={() => onPreview?.(kost)}
              >
                <Eye className="h-4 w-4 text-gray-600" />
              </Button>
            </div>
          </div>
          
          {/* Type Badge */}
          <div className="absolute top-3 left-3">
            <Badge className={getTypeColor(kost.type)}>
              Kost {kost.type.charAt(0).toUpperCase() + kost.type.slice(1)}
            </Badge>
          </div>
          
          {/* Available Rooms */}
          <div className="absolute bottom-3 left-3">
            <Badge variant="secondary" className="bg-white/90 text-gray-800">
              {kost.available} kamar tersedia
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Title and Location */}
          <div>
            <h3 className="font-semibold text-lg line-clamp-1 group-hover:text-primary transition-colors">
              {kost.title}
            </h3>
            <div className="flex items-center gap-1 text-sm text-muted-foreground mt-1">
              <MapPin className="h-3 w-3" />
              <span className="line-clamp-1">{kost.location}</span>
            </div>
          </div>
          
          {/* Rating */}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span className="font-medium text-sm">{kost.rating}</span>
            </div>
            <span className="text-sm text-muted-foreground">
              ({kost.reviewCount} ulasan)
            </span>
          </div>
          
          {/* Facilities */}
          <div className="flex flex-wrap gap-2">
            {kost.facilities.slice(0, 3).map((facility) => {
              const IconComponent = facilityIcons[facility.toLowerCase()] || Shield
              return (
                <div
                  key={facility}
                  className="flex items-center gap-1 text-xs text-muted-foreground bg-muted px-2 py-1 rounded-md"
                >
                  <IconComponent className="h-3 w-3 flex-shrink-0" />
                  <span className="truncate">{facility}</span>
                </div>
              )
            })}
            {kost.facilities.length > 3 && (
              <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-md">
                +{kost.facilities.length - 3} lainnya
              </div>
            )}
          </div>
        </div>
      </CardContent>
      
      <Separator />
      
      <CardFooter className="p-4 pt-3">
        <div className="flex items-center justify-between w-full">
          <div>
            <div className="text-2xl font-bold text-primary">
              {formatPrice(kost.price)}
            </div>
            <div className="text-sm text-muted-foreground">per bulan</div>
          </div>
          
          <div className="flex gap-2 flex-col sm:flex-row">
            {onCompare && (
              <Button
                variant={isComparing ? "default" : "outline"}
                size="sm"
                onClick={() => onCompare(kost.id)}
                className="flex-1 sm:flex-none"
              >
                {isComparing ? "Terpilih" : "Bandingkan"}
              </Button>
            )}
            <Button
              size="sm"
              onClick={() => onPreview?.(kost)}
              className="flex-1 sm:flex-none"
            >
              Lihat Detail
            </Button>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
