"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[172,352],{7172:(e,a,s)=>{s.r(a),s.d(a,{KostPreviewDialog:()=>O});var t=s(2273),l=s(5461),i=s(7466),r=s(7352),n=s(7320),c=s(2521),d=s(4455),o=s(1415);function m(e){let{className:a,...s}=e;return(0,t.jsx)(d.bL,{"data-slot":"tabs",className:(0,o.cn)("flex flex-col gap-2",a),...s})}function u(e){let{className:a,...s}=e;return(0,t.jsx)(d.B8,{"data-slot":"tabs-list",className:(0,o.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...s})}function x(e){let{className:a,...s}=e;return(0,t.jsx)(d.l9,{"data-slot":"tabs-trigger",className:(0,o.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...s})}function p(e){let{className:a,...s}=e;return(0,t.jsx)(d.UC,{"data-slot":"tabs-content",className:(0,o.cn)("flex-1 outline-none",a),...s})}var f=s(8),h=s(4078),g=s(5383);let j=l.createContext(null);function v(){let e=l.useContext(j);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}function N(e){let{orientation:a="horizontal",opts:s,setApi:i,plugins:r,className:n,children:c,...d}=e,[m,u]=(0,f.A)({...s,axis:"horizontal"===a?"x":"y"},r),[x,p]=l.useState(!1),[h,g]=l.useState(!1),v=l.useCallback(e=>{e&&(p(e.canScrollPrev()),g(e.canScrollNext()))},[]),N=l.useCallback(()=>{null==u||u.scrollPrev()},[u]),b=l.useCallback(()=>{null==u||u.scrollNext()},[u]),w=l.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),N()):"ArrowRight"===e.key&&(e.preventDefault(),b())},[N,b]);return l.useEffect(()=>{u&&i&&i(u)},[u,i]),l.useEffect(()=>{if(u)return v(u),u.on("reInit",v),u.on("select",v),()=>{null==u||u.off("select",v)}},[u,v]),(0,t.jsx)(j.Provider,{value:{carouselRef:m,api:u,opts:s,orientation:a||((null==s?void 0:s.axis)==="y"?"vertical":"horizontal"),scrollPrev:N,scrollNext:b,canScrollPrev:x,canScrollNext:h},children:(0,t.jsx)("div",{onKeyDownCapture:w,className:(0,o.cn)("relative",n),role:"region","aria-roledescription":"carousel","data-slot":"carousel",...d,children:c})})}function b(e){let{className:a,...s}=e,{carouselRef:l,orientation:i}=v();return(0,t.jsx)("div",{ref:l,className:"overflow-hidden","data-slot":"carousel-content",children:(0,t.jsx)("div",{className:(0,o.cn)("flex","horizontal"===i?"-ml-4":"-mt-4 flex-col",a),...s})})}function w(e){let{className:a,...s}=e,{orientation:l}=v();return(0,t.jsx)("div",{role:"group","aria-roledescription":"slide","data-slot":"carousel-item",className:(0,o.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===l?"pl-4":"pt-4",a),...s})}function k(e){let{className:a,variant:s="outline",size:l="icon",...i}=e,{orientation:r,scrollPrev:c,canScrollPrev:d}=v();return(0,t.jsxs)(n.$,{"data-slot":"carousel-previous",variant:s,size:l,className:(0,o.cn)("absolute size-8 rounded-full","horizontal"===r?"top-1/2 -left-12 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",a),disabled:!d,onClick:c,...i,children:[(0,t.jsx)(h.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Previous slide"})]})}function y(e){let{className:a,variant:s="outline",size:l="icon",...i}=e,{orientation:r,scrollNext:c,canScrollNext:d}=v();return(0,t.jsxs)(n.$,{"data-slot":"carousel-next",variant:s,size:l,className:(0,o.cn)("absolute size-8 rounded-full","horizontal"===r?"top-1/2 -right-12 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",a),disabled:!d,onClick:c,...i,children:[(0,t.jsx)(g.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Next slide"})]})}var A=s(1821),z=s(87),C=s(8140),D=s(2814),P=s(428),_=s(74),L=s(5573),$=s(8513),S=s(9269),E=s(1101),K=s(1991),I=s(6309),W=s(2497),B=s(8049),F=s(4529),U=s(5557),G=s(5448),R=s(667),T=s(8311);let J={wifi:A.A,parkir:z.A,dapur:C.A,listrik:D.A,air:P.A,keamanan:_.A,"ruang tamu":L.A,ac:D.A,kasur:$.A,lemari:S.A},Y=[{id:"1",user:"Andi Pratama",rating:5,comment:"Kost yang sangat nyaman dan bersih. Pemilik ramah dan fasilitas lengkap.",date:"2024-01-15",avatar:T.Y$.avatars.male1},{id:"2",user:"Sari Dewi",rating:4,comment:"Lokasi strategis dekat kampus. WiFi cepat dan kamar luas.",date:"2024-01-10",avatar:T.Y$.avatars.female1}],H=["Jam malam pukul 22.00 WIB","Dilarang membawa tamu menginap","Dilarang merokok di dalam kamar","Wajib menjaga kebersihan bersama","Pembayaran dilakukan setiap tanggal 1"];function O(e){let a,{kost:s,isOpen:d,onClose:f,onWishlist:h,onCompare:g,isComparing:j=!1}=e,[v,A]=(0,l.useState)(0);return s?(0,t.jsx)(r.lG,{open:d,onOpenChange:f,children:(0,t.jsxs)(r.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto custom-scrollbar",children:[(0,t.jsx)(r.c7,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(r.L3,{className:"text-2xl font-bold",children:s.title}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground",children:[(0,t.jsx)(E.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:s.location})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(K.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,t.jsx)("span",{className:"font-medium",children:s.rating}),(0,t.jsxs)("span",{className:"text-muted-foreground",children:["(",s.reviewCount," ulasan)"]})]}),(0,t.jsxs)(c.E,{className:(e=>{switch(e){case"putra":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";case"putri":return"bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300";case"campur":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"}})(s.type),children:["Kost ",s.type.charAt(0).toUpperCase()+s.type.slice(1)]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>null==h?void 0:h(s.id),children:[(0,t.jsx)(I.A,{className:(0,o.cn)("h-4 w-4 mr-2",s.isWishlisted?"fill-red-500 text-red-500":"")}),s.isWishlisted?"Tersimpan":"Simpan"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"Bagikan"]})]})]})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)(N,{className:"w-full",children:[(0,t.jsx)(b,{children:s.images.map((e,a)=>(0,t.jsx)(w,{children:(0,t.jsx)("div",{className:"relative aspect-[16/9] overflow-hidden rounded-lg",children:(0,t.jsx)(i.default,{src:e,alt:"".concat(s.title," - Gambar ").concat(a+1),fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 800px"})})},a))}),(0,t.jsx)(k,{className:"left-4"}),(0,t.jsx)(y,{className:"right-4"})]})}),(0,t.jsxs)(m,{defaultValue:"overview",className:"w-full",children:[(0,t.jsxs)(u,{className:"grid w-full grid-cols-4",children:[(0,t.jsx)(x,{value:"overview",children:"Ringkasan"}),(0,t.jsx)(x,{value:"facilities",children:"Fasilitas"}),(0,t.jsx)(x,{value:"reviews",children:"Ulasan"}),(0,t.jsx)(x,{value:"rules",children:"Peraturan"})]}),(0,t.jsx)(p,{value:"overview",className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-lg mb-2",children:"Deskripsi"}),(0,t.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:s.description})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-lg mb-2",children:"Detail Kost"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)($.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("span",{className:"text-sm",children:[s.available," kamar tersedia"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("span",{className:"text-sm",children:["Tipe: Kost ",s.type]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(B.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm",children:"Pembayaran bulanan"})]})]})]})]}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"bg-muted p-4 rounded-lg",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-primary mb-1",children:(a=s.price,new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(a))}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground mb-4",children:"per bulan"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(n.$,{className:"w-full",size:"lg",children:[(0,t.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"Hubungi Pemilik"]}),(0,t.jsxs)(n.$,{variant:"outline",className:"w-full",size:"lg",children:[(0,t.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Chat WhatsApp"]}),g&&(0,t.jsx)(n.$,{variant:j?"default":"outline",className:"w-full",size:"lg",onClick:()=>g(s.id),children:j?"Terpilih untuk Perbandingan":"Bandingkan Kost"})]})]})})]})}),(0,t.jsx)(p,{value:"facilities",className:"space-y-4",children:(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:s.facilities.map(e=>{let a=J[e.toLowerCase()]||_.A;return(0,t.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted rounded-lg",children:[(0,t.jsx)(a,{className:"h-5 w-5 text-primary"}),(0,t.jsx)("span",{className:"font-medium",children:e})]},e)})})}),(0,t.jsx)(p,{value:"reviews",className:"space-y-4",children:(0,t.jsx)("div",{className:"space-y-4",children:Y.map(e=>(0,t.jsx)("div",{className:"border rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-muted rounded-full flex items-center justify-center",children:(0,t.jsx)(G.A,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)("span",{className:"font-medium",children:e.user}),(0,t.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:e.rating}).map((e,a)=>(0,t.jsx)(K.A,{className:"h-3 w-3 fill-yellow-400 text-yellow-400"},a))})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:e.comment}),(0,t.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,t.jsx)(R.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:new Date(e.date).toLocaleDateString("id-ID")})]})]})]})},e.id))})}),(0,t.jsx)(p,{value:"rules",className:"space-y-4",children:(0,t.jsx)("div",{className:"space-y-3",children:H.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted rounded-lg",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium mt-0.5",children:a+1}),(0,t.jsx)("span",{children:e})]},a))})})]})]})]})}):null}},7352:(e,a,s)=>{s.d(a,{Cf:()=>o,L3:()=>u,c7:()=>m,lG:()=>n});var t=s(2273);s(5461);var l=s(9472),i=s(9889),r=s(1415);function n(e){let{...a}=e;return(0,t.jsx)(l.bL,{"data-slot":"dialog",...a})}function c(e){let{...a}=e;return(0,t.jsx)(l.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(l.hJ,{"data-slot":"dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...s})}function o(e){let{className:a,children:s,showCloseButton:n=!0,...o}=e;return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(l.UC,{"data-slot":"dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[s,n&&(0,t.jsxs)(l.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(i.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",a),...s})}function u(e){let{className:a,...s}=e;return(0,t.jsx)(l.hE,{"data-slot":"dialog-title",className:(0,r.cn)("text-lg leading-none font-semibold",a),...s})}}}]);