module.exports = {

"[project]/app/listings/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>ListingsLayout,
    "metadata": ()=>metadata
});
const metadata = {
    title: "Pencarian Kost - KostHub",
    description: "Temukan kost terbaik dengan fitur pencarian canggih, filter detail, dan perbandingan interaktif. Ribuan pilihan kost terverifikasi di seluruh Indonesia.",
    keywords: [
        "pencarian kost",
        "daftar kost",
        "kost terdekat",
        "sewa kamar",
        "boarding house"
    ],
    openGraph: {
        title: "Pencarian Kost - KostHub",
        description: "Temukan kost terbaik dengan fitur pencarian canggih dan filter detail.",
        images: [
            "/og-listings.jpg"
        ]
    }
};
function ListingsLayout({ children }) {
    return children;
}
}),

};

//# sourceMappingURL=app_listings_layout_tsx_317bf357._.js.map