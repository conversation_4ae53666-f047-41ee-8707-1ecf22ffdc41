"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock,
  MessageCircle,
  Send,
  CheckCircle
} from "lucide-react"

const contactInfo = [
  {
    icon: Mail,
    title: "Email",
    value: "<EMAIL>",
    description: "Kirim email untuk pertanyaan umum"
  },
  {
    icon: Phone,
    title: "Telepon",
    value: "+62 21 1234 5678",
    description: "Hubungi kami di jam kerja"
  },
  {
    icon: MapPin,
    title: "<PERSON><PERSON><PERSON>",
    value: "Jakarta, Indonesia",
    description: "Kantor pusat kami"
  },
  {
    icon: Clock,
    title: "Jam Operasional",
    value: "Senin - Jumat, 09:00 - 18:00",
    description: "Waktu layanan customer service"
  }
]

const faqItems = [
  {
    question: "Bagaimana cara mencari kost di KostHub?",
    answer: "Anda dapat menggunakan fitur pencarian di halaman utama dengan memasukkan lokasi, budget, atau fasilitas yang diinginkan."
  },
  {
    question: "Apakah semua kost sudah terverifikasi?",
    answer: "Ya, semua kost yang terdaftar di KostHub telah melalui proses verifikasi untuk memastikan kualitas dan keamanan."
  },
  {
    question: "Bagaimana cara menggunakan fitur perbandingan?",
    answer: "Klik tombol 'Bandingkan' pada kost yang ingin dibandingkan, lalu klik tombol floating 'Bandingkan' untuk melihat perbandingan detail."
  },
  {
    question: "Apakah ada biaya untuk menggunakan platform ini?",
    answer: "Tidak, KostHub gratis untuk digunakan oleh pencari kost. Kami tidak mengenakan biaya apapun."
  }
]

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    category: "",
    message: ""
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setIsSubmitted(true)
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false)
      setFormData({
        name: "",
        email: "",
        subject: "",
        category: "",
        message: ""
      })
    }, 3000)
  }

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary/10 via-background to-accent/10">
        <div className="container mx-auto px-4 text-center">
          <Badge variant="outline" className="mb-6">
            <MessageCircle className="h-4 w-4 mr-2" />
            Hubungi Kami
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Ada Pertanyaan?
            <br />
            <span className="text-primary">Kami Siap Membantu</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Tim customer service kami siap membantu Anda 24/7. Jangan ragu untuk menghubungi 
            kami jika ada pertanyaan atau butuh bantuan dalam mencari kost impian.
          </p>
        </div>
      </section>

      {/* Contact Info */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {contactInfo.map((info, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <info.icon className="h-12 w-12 text-primary mx-auto mb-4" />
                  <CardTitle className="text-xl">{info.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="font-semibold mb-2">{info.value}</p>
                  <p className="text-muted-foreground text-sm">
                    {info.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Contact Form */}
          <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            <div>
              <h2 className="text-3xl font-bold mb-6">Kirim Pesan</h2>
              <p className="text-muted-foreground mb-8">
                Isi form di bawah ini dan kami akan merespons dalam 24 jam.
              </p>

              <Card className="p-6">
                {isSubmitted ? (
                  <div className="text-center py-8">
                    <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">Pesan Terkirim!</h3>
                    <p className="text-muted-foreground">
                      Terima kasih atas pesan Anda. Tim kami akan segera merespons.
                    </p>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Nama Lengkap</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => handleChange("name", e.target.value)}
                          placeholder="Masukkan nama lengkap"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleChange("email", e.target.value)}
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="category">Kategori</Label>
                      <Select value={formData.category} onValueChange={(value) => handleChange("category", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih kategori pertanyaan" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="general">Pertanyaan Umum</SelectItem>
                          <SelectItem value="technical">Masalah Teknis</SelectItem>
                          <SelectItem value="partnership">Kerjasama</SelectItem>
                          <SelectItem value="complaint">Keluhan</SelectItem>
                          <SelectItem value="suggestion">Saran</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="subject">Subjek</Label>
                      <Input
                        id="subject"
                        value={formData.subject}
                        onChange={(e) => handleChange("subject", e.target.value)}
                        placeholder="Subjek pesan"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message">Pesan</Label>
                      <Textarea
                        id="message"
                        value={formData.message}
                        onChange={(e) => handleChange("message", e.target.value)}
                        placeholder="Tulis pesan Anda di sini..."
                        rows={6}
                        required
                      />
                    </div>

                    <Button 
                      type="submit" 
                      className="w-full" 
                      size="lg"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        "Mengirim..."
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          Kirim Pesan
                        </>
                      )}
                    </Button>
                  </form>
                )}
              </Card>
            </div>

            {/* FAQ Section */}
            <div>
              <h2 className="text-3xl font-bold mb-6">FAQ</h2>
              <p className="text-muted-foreground mb-8">
                Pertanyaan yang sering diajukan oleh pengguna kami.
              </p>

              <div className="space-y-4">
                {faqItems.map((item, index) => (
                  <Card key={index} className="p-6">
                    <h3 className="font-semibold mb-3">{item.question}</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {item.answer}
                    </p>
                  </Card>
                ))}
              </div>

              <Card className="p-6 mt-6 bg-primary/5 border-primary/20">
                <h3 className="font-semibold mb-2">Tidak menemukan jawaban?</h3>
                <p className="text-muted-foreground mb-4">
                  Jika pertanyaan Anda tidak terjawab di FAQ, jangan ragu untuk menghubungi kami langsung.
                </p>
                <Button variant="outline" className="w-full">
                  <Mail className="h-4 w-4 mr-2" />
                  Email Kami
                </Button>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
