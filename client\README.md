# KostHub - Platform Pencarian Kost Inovatif

KostHub adalah platform pencarian kost modern yang dibangun dengan Next.js 15, React 19, dan shadcn/ui. Platform ini menyediakan fitur-fitur inovatif seperti preview dinamis dan perbandingan kost interaktif untuk membantu pengguna menemukan tempat tinggal yang ideal.

## 🚀 Fitur Utama

### 🏠 **MVP Terms untuk Website Kost Inovatif**
- **Preview Dinamis**: Modal interaktif dengan carousel gambar, detail lengkap, dan informasi fasilitas
- **Perbandingan Kost**: Bandingkan hingga 3 kost sekaligus dengan tabel perbandingan detail
- **Pencarian Canggih**: Filter berdasarkan lokasi, harga, tipe, dan fasilitas
- **Responsive Design**: Optimized untuk desktop, tablet, dan mobile
- **SEO Optimized**: Meta tags, sitemap, dan robots.txt untuk SEO yang baik

### 🎯 **Tujuan MVP**
- Memvalidasi minat pasar terhadap fitur pencarian kost yang detail
- Mengu<PERSON>r kemudahan penggunaan oleh pencari kost
- Menguji inovasi unggulan: preview dinamis dan perbandingan interaktif

## 🛠️ Tech Stack

- **Framework**: Next.js 15.4.2 (App Router)
- **React**: 19.1.0 dengan hooks dan functional components
- **TypeScript**: Untuk type safety yang lebih baik
- **Styling**: Tailwind CSS v4 dengan custom theme
- **UI Components**: shadcn/ui dengan Radix UI primitives
- **Icons**: Lucide React
- **Form Handling**: React Hook Form dengan Zod validation
- **State Management**: React hooks (useState, useEffect, custom hooks)

## 📦 Instalasi

1. **Clone repository**
```bash
git clone <repository-url>
cd kost/client
```

2. **Install dependencies**
```bash
npm install
```

3. **Jalankan development server**
```bash
npm run dev
```

4. **Buka browser**
```
http://localhost:3000
```

## 🏗️ Struktur Project

```
client/
├── app/                    # Next.js App Router
│   ├── about/             # Halaman tentang
│   ├── contact/           # Halaman kontak
│   ├── listings/          # Halaman pencarian kost
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Homepage
│   ├── robots.ts          # SEO robots.txt
│   └── sitemap.ts         # SEO sitemap
├── components/            # Reusable components
│   ├── ui/               # shadcn/ui components
│   ├── comparison-dialog.tsx
│   ├── error-boundary.tsx
│   ├── footer.tsx
│   ├── hero-section.tsx
│   ├── kost-card.tsx
│   ├── kost-preview-dialog.tsx
│   ├── loading.tsx
│   ├── navigation.tsx
│   └── search-bar.tsx
├── lib/                   # Utilities
│   ├── constants.ts       # App constants
│   ├── format.ts          # Formatting utilities
│   ├── hooks.ts           # Custom hooks
│   └── utils.ts           # General utilities
└── public/               # Static assets
```

## 🎨 Design System

### **Color Palette**
- **Primary**: Blue-purple gradient untuk brand identity
- **Secondary**: Orange accent untuk highlights
- **Success**: Green untuk status positif
- **Warning**: Yellow untuk peringatan
- **Neutral**: Gray scale untuk teks dan backgrounds

### **Typography**
- **Font**: Geist Sans untuk UI, Geist Mono untuk code
- **Scale**: Responsive typography dengan Tailwind CSS

### **Components**
- **Reusable**: Semua komponen dibuat reusable dan tidak redundant
- **Accessible**: Menggunakan ARIA attributes dan semantic HTML
- **Responsive**: Mobile-first design approach

## 🔧 Fitur Teknis

### **Performance Optimizations**
- **Lazy Loading**: Komponen berat dimuat secara lazy
- **Image Optimization**: Next.js Image component dengan proper sizing
- **Code Splitting**: Automatic code splitting dengan Next.js
- **Caching**: Browser caching untuk static assets

### **Error Handling**
- **Error Boundaries**: Menangkap dan menampilkan error dengan graceful
- **Loading States**: Skeleton loading untuk better UX
- **Form Validation**: Client-side validation dengan Zod

### **SEO & Accessibility**
- **Meta Tags**: Comprehensive meta tags untuk setiap halaman
- **Structured Data**: Schema markup untuk search engines
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader**: ARIA labels dan semantic HTML

## 📱 Responsive Design

- **Mobile First**: Desain dimulai dari mobile kemudian desktop
- **Breakpoints**: sm (640px), md (768px), lg (1024px), xl (1280px)
- **Touch Friendly**: Button sizes dan touch targets yang optimal
- **Performance**: Optimized untuk koneksi mobile yang lambat

## 🧪 Testing & Quality

### **Code Quality**
- **TypeScript**: Strict mode untuk type safety
- **ESLint**: Code linting untuk consistency
- **Prettier**: Code formatting
- **Component Testing**: Manual testing untuk semua komponen

### **Browser Support**
- **Modern Browsers**: Chrome, Firefox, Safari, Edge
- **Mobile Browsers**: iOS Safari, Chrome Mobile
- **Progressive Enhancement**: Graceful degradation untuk browser lama

## 🚀 Deployment

### **Build Production**
```bash
npm run build
npm run start
```

### **Environment Variables**
```env
NEXT_PUBLIC_APP_URL=https://kosthub.com
NEXT_PUBLIC_API_URL=https://api.kosthub.com
```

## 📊 Features Overview

### **Homepage**
- Hero section dengan search bar utama
- Featured kost dengan rating dan harga
- Testimonial pengguna
- Call-to-action sections

### **Listings Page**
- Grid/List view toggle
- Advanced filtering (lokasi, harga, tipe, fasilitas)
- Sorting options (harga, rating, terbaru)
- Pagination
- Real-time search

### **Kost Preview Dialog**
- Image carousel dengan navigation
- Tabbed content (overview, fasilitas, ulasan, peraturan)
- Contact actions (telepon, WhatsApp)
- Wishlist dan comparison actions

### **Comparison Feature**
- Side-by-side comparison hingga 3 kost
- Detailed facility comparison
- Price and rating comparison
- Action buttons untuk setiap kost

### **Navigation & Footer**
- Responsive navigation dengan mobile menu
- Footer dengan links dan contact info
- Social media integration
- Legal pages (terms, privacy)

## 🎯 MVP Validation Metrics

### **User Experience Metrics**
- Time to find relevant kost
- Conversion rate dari preview ke contact
- Usage rate fitur perbandingan
- Mobile vs desktop usage patterns

### **Technical Metrics**
- Page load speed (< 3 seconds)
- Core Web Vitals scores
- Error rates dan crash reports
- API response times

## 🔮 Future Enhancements

- **Backend Integration**: API untuk data real-time
- **User Authentication**: Login/register system
- **Favorites System**: Save kost untuk nanti
- **Reviews & Ratings**: User-generated content
- **Map Integration**: Google Maps untuk lokasi
- **Push Notifications**: Alert untuk kost baru
- **Advanced Filters**: More granular filtering options
- **Virtual Tours**: 360° photos dan videos

## 👥 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

Untuk pertanyaan atau dukungan:
- Email: <EMAIL>
- Phone: +62 21 1234 5678
- Website: https://kosthub.com

---

**KostHub** - Revolusi Pencarian Kost di Indonesia 🏠✨
