"use strict";exports.id=52,exports.ids=[52],exports.modules={4091:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"Image",{enumerable:!0,get:function(){return u}});let d=c(79809),e=c(65148),f=c(21157),g=e._(c(43616)),h=d._(c(44145)),i=d._(c(30650)),j=c(16675),k=c(34718),l=c(30165);c(65258);let m=c(46486),n=d._(c(54963)),o=c(73756),p={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function q(a,b,c,d,e,f,g){let h=null==a?void 0:a.src;a&&a["data-loaded-src"]!==h&&(a["data-loaded-src"]=h,("decode"in a?a.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(a.parentElement&&a.isConnected){if("empty"!==b&&e(!0),null==c?void 0:c.current){let b=new Event("load");Object.defineProperty(b,"target",{writable:!1,value:a});let d=!1,e=!1;c.current({...b,nativeEvent:b,currentTarget:a,target:a,isDefaultPrevented:()=>d,isPropagationStopped:()=>e,persist:()=>{},preventDefault:()=>{d=!0,b.preventDefault()},stopPropagation:()=>{e=!0,b.stopPropagation()}})}(null==d?void 0:d.current)&&d.current(a)}}))}function r(a){return g.use?{fetchPriority:a}:{fetchpriority:a}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let s=(0,g.forwardRef)((a,b)=>{let{src:c,srcSet:d,sizes:e,height:h,width:i,decoding:j,className:k,style:l,fetchPriority:m,placeholder:n,loading:p,unoptimized:s,fill:t,onLoadRef:u,onLoadingCompleteRef:v,setBlurComplete:w,setShowAltText:x,sizesInput:y,onLoad:z,onError:A,...B}=a,C=(0,g.useCallback)(a=>{a&&(A&&(a.src=a.src),a.complete&&q(a,n,u,v,w,s,y))},[c,n,u,v,w,A,s,y]),D=(0,o.useMergedRef)(b,C);return(0,f.jsx)("img",{...B,...r(m),loading:p,width:i,height:h,decoding:j,"data-nimg":t?"fill":"1",className:k,style:l,sizes:e,srcSet:d,src:c,ref:D,onLoad:a=>{q(a.currentTarget,n,u,v,w,s,y)},onError:a=>{x(!0),"empty"!==n&&w(!0),A&&A(a)}})});function t(a){let{isAppRouter:b,imgAttributes:c}=a,d={as:"image",imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:c.crossOrigin,referrerPolicy:c.referrerPolicy,...r(c.fetchPriority)};return b&&h.default.preload?(h.default.preload(c.src,d),null):(0,f.jsx)(i.default,{children:(0,f.jsx)("link",{rel:"preload",href:c.srcSet?void 0:c.src,...d},"__nimg-"+c.src+c.srcSet+c.sizes)})}let u=(0,g.forwardRef)((a,b)=>{let c=(0,g.useContext)(m.RouterContext),d=(0,g.useContext)(l.ImageConfigContext),e=(0,g.useMemo)(()=>{var a;let b=p||d||k.imageConfigDefault,c=[...b.deviceSizes,...b.imageSizes].sort((a,b)=>a-b),e=b.deviceSizes.sort((a,b)=>a-b),f=null==(a=b.qualities)?void 0:a.sort((a,b)=>a-b);return{...b,allSizes:c,deviceSizes:e,qualities:f}},[d]),{onLoad:h,onLoadingComplete:i}=a,o=(0,g.useRef)(h);(0,g.useEffect)(()=>{o.current=h},[h]);let q=(0,g.useRef)(i);(0,g.useEffect)(()=>{q.current=i},[i]);let[r,u]=(0,g.useState)(!1),[v,w]=(0,g.useState)(!1),{props:x,meta:y}=(0,j.getImgProps)(a,{defaultLoader:n.default,imgConf:e,blurComplete:r,showAltText:v});return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(s,{...x,unoptimized:y.unoptimized,placeholder:y.placeholder,fill:y.fill,onLoadRef:o,onLoadingCompleteRef:q,setBlurComplete:u,setShowAltText:w,sizesInput:a.sizes,ref:b}),y.priority?(0,f.jsx)(t,{isAppRouter:!c,imgAttributes:x}):null]})});("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},8932:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},14098:(a,b)=>{function c(a){let{widthInt:b,heightInt:c,blurWidth:d,blurHeight:e,blurDataURL:f,objectFit:g}=a,h=d?40*d:b,i=e?40*e:c,j=h&&i?"viewBox='0 0 "+h+" "+i+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+j+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(j?"none":"contain"===g?"xMidYMid":"cover"===g?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+f+"'/%3E%3C/svg%3E"}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"getImageBlurSvg",{enumerable:!0,get:function(){return c}})},14441:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return f}});let d=c(43616),e=()=>{};function f(a){var b;let{headManager:c,reduceComponentsToState:f}=a;function g(){if(c&&c.mountedInstances){let b=d.Children.toArray(Array.from(c.mountedInstances).filter(Boolean));c.updateHead(f(b,a))}}return null==c||null==(b=c.mountedInstances)||b.add(a.children),g(),e(()=>{var b;return null==c||null==(b=c.mountedInstances)||b.add(a.children),()=>{var b;null==c||null==(b=c.mountedInstances)||b.delete(a.children)}}),e(()=>(c&&(c._pendingUpdate=g),()=>{c&&(c._pendingUpdate=g)})),null}},16675:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"getImgProps",{enumerable:!0,get:function(){return i}}),c(65258);let d=c(14098),e=c(34718),f=["-moz-initial","fill","none","scale-down",void 0];function g(a){return void 0!==a.default}function h(a){return void 0===a?a:"number"==typeof a?Number.isFinite(a)?a:NaN:"string"==typeof a&&/^[0-9]+$/.test(a)?parseInt(a,10):NaN}function i(a,b){var c,i;let j,k,l,{src:m,sizes:n,unoptimized:o=!1,priority:p=!1,loading:q,className:r,quality:s,width:t,height:u,fill:v=!1,style:w,overrideSrc:x,onLoad:y,onLoadingComplete:z,placeholder:A="empty",blurDataURL:B,fetchPriority:C,decoding:D="async",layout:E,objectFit:F,objectPosition:G,lazyBoundary:H,lazyRoot:I,...J}=a,{imgConf:K,showAltText:L,blurComplete:M,defaultLoader:N}=b,O=K||e.imageConfigDefault;if("allSizes"in O)j=O;else{let a=[...O.deviceSizes,...O.imageSizes].sort((a,b)=>a-b),b=O.deviceSizes.sort((a,b)=>a-b),d=null==(c=O.qualities)?void 0:c.sort((a,b)=>a-b);j={...O,allSizes:a,deviceSizes:b,qualities:d}}if(void 0===N)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let P=J.loader||N;delete J.loader,delete J.srcSet;let Q="__next_img_default"in P;if(Q){if("custom"===j.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let a=P;P=b=>{let{config:c,...d}=b;return a(d)}}if(E){"fill"===E&&(v=!0);let a={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];a&&(w={...w,...a});let b={responsive:"100vw",fill:"100vw"}[E];b&&!n&&(n=b)}let R="",S=h(t),T=h(u);if((i=m)&&"object"==typeof i&&(g(i)||void 0!==i.src)){let a=g(m)?m.default:m;if(!a.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!a.height||!a.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(k=a.blurWidth,l=a.blurHeight,B=B||a.blurDataURL,R=a.src,!v)if(S||T){if(S&&!T){let b=S/a.width;T=Math.round(a.height*b)}else if(!S&&T){let b=T/a.height;S=Math.round(a.width*b)}}else S=a.width,T=a.height}let U=!p&&("lazy"===q||void 0===q);(!(m="string"==typeof m?m:R)||m.startsWith("data:")||m.startsWith("blob:"))&&(o=!0,U=!1),j.unoptimized&&(o=!0),Q&&!j.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(o=!0);let V=h(s),W=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:F,objectPosition:G}:{},L?{}:{color:"transparent"},w),X=M||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,d.getImageBlurSvg)({widthInt:S,heightInt:T,blurWidth:k,blurHeight:l,blurDataURL:B||"",objectFit:W.objectFit})+'")':'url("'+A+'")',Y=f.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,Z=X?{backgroundSize:Y,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},$=function(a){let{config:b,src:c,unoptimized:d,width:e,quality:f,sizes:g,loader:h}=a;if(d)return{src:c,srcSet:void 0,sizes:void 0};let{widths:i,kind:j}=function(a,b,c){let{deviceSizes:d,allSizes:e}=a;if(c){let a=/(^|\s)(1?\d?\d)vw/g,b=[];for(let d;d=a.exec(c);)b.push(parseInt(d[2]));if(b.length){let a=.01*Math.min(...b);return{widths:e.filter(b=>b>=d[0]*a),kind:"w"}}return{widths:e,kind:"w"}}return"number"!=typeof b?{widths:d,kind:"w"}:{widths:[...new Set([b,2*b].map(a=>e.find(b=>b>=a)||e[e.length-1]))],kind:"x"}}(b,e,g),k=i.length-1;return{sizes:g||"w"!==j?g:"100vw",srcSet:i.map((a,d)=>h({config:b,src:c,quality:f,width:a})+" "+("w"===j?a:d+1)+j).join(", "),src:h({config:b,src:c,quality:f,width:i[k]})}}({config:j,src:m,unoptimized:o,width:S,quality:V,sizes:n,loader:P});return{props:{...J,loading:U?"lazy":q,fetchPriority:C,width:S,height:T,decoding:D,className:r,style:{...W,...Z},sizes:$.sizes,srcSet:$.srcSet,src:x||$.src},meta:{unoptimized:o,priority:p,placeholder:A,fill:v}}}},18080:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]])},30165:(a,b,c)=>{a.exports=c(58807).vendored.contexts.ImageConfigContext},30650:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return p},defaultHead:function(){return l}});let d=c(79809),e=c(65148),f=c(21157),g=e._(c(43616)),h=d._(c(14441)),i=c(74833),j=c(52715),k=c(32610);function l(a){void 0===a&&(a=!1);let b=[(0,f.jsx)("meta",{charSet:"utf-8"},"charset")];return a||b.push((0,f.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),b}function m(a,b){return"string"==typeof b||"number"==typeof b?a:b.type===g.default.Fragment?a.concat(g.default.Children.toArray(b.props.children).reduce((a,b)=>"string"==typeof b||"number"==typeof b?a:a.concat(b),[])):a.concat(b)}c(65258);let n=["name","httpEquiv","charSet","itemProp"];function o(a,b){let{inAmpMode:c}=b;return a.reduce(m,[]).reverse().concat(l(c).reverse()).filter(function(){let a=new Set,b=new Set,c=new Set,d={};return e=>{let f=!0,g=!1;if(e.key&&"number"!=typeof e.key&&e.key.indexOf("$")>0){g=!0;let b=e.key.slice(e.key.indexOf("$")+1);a.has(b)?f=!1:a.add(b)}switch(e.type){case"title":case"base":b.has(e.type)?f=!1:b.add(e.type);break;case"meta":for(let a=0,b=n.length;a<b;a++){let b=n[a];if(e.props.hasOwnProperty(b))if("charSet"===b)c.has(b)?f=!1:c.add(b);else{let a=e.props[b],c=d[b]||new Set;("name"!==b||!g)&&c.has(a)?f=!1:(c.add(a),d[b]=c)}}}return f}}()).reverse().map((a,b)=>{let c=a.key||b;return g.default.cloneElement(a,{key:c})})}let p=function(a){let{children:b}=a,c=(0,g.useContext)(i.AmpStateContext),d=(0,g.useContext)(j.HeadManagerContext);return(0,f.jsx)(h.default,{reduceComponentsToState:o,headManager:d,inAmpMode:(0,k.isInAmpMode)(c),children:b})};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},32610:(a,b)=>{function c(a){let{ampFirst:b=!1,hybrid:c=!1,hasQuery:d=!1}=void 0===a?{}:a;return b||c&&d}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isInAmpMode",{enumerable:!0,get:function(){return c}})},34718:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{VALID_LOADERS:function(){return c},imageConfigDefault:function(){return d}});let c=["default","imgix","cloudinary","akamai","custom"],d={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},38163:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},39401:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},45817:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},46486:(a,b,c)=>{a.exports=c(58807).vendored.contexts.RouterContext},52715:(a,b,c)=>{a.exports=c(58807).vendored.contexts.HeadManagerContext},54963:(a,b)=>{function c(a){var b;let{config:c,src:d,width:e,quality:f}=a,g=f||(null==(b=c.qualities)?void 0:b.reduce((a,b)=>Math.abs(b-75)<Math.abs(a-75)?b:a))||75;return c.path+"?url="+encodeURIComponent(d)+"&w="+e+"&q="+g+(d.startsWith("/_next/static/media/"),"")}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return d}}),c.__next_img_default=!0;let d=c},68307:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},70696:(a,b,c)=>{c.d(b,{default:()=>e.a});var d=c(91147),e=c.n(d)},71960:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]])},74833:(a,b,c)=>{a.exports=c(58807).vendored.contexts.AmpContext},81986:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88358:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},89122:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},91123:(a,b,c)=>{c.d(b,{CC:()=>R,Q6:()=>S,bL:()=>Q,zi:()=>T});var d=c(43616),e=c(21311),f=c(86984),g=c(84677),h=c(37784),i=c(55002),j=c(78309),k=c(20355),l=c(62115),m=c(7957),n=c(88036),o=c(21157),p=["PageUp","PageDown"],q=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],r={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},s="Slider",[t,u,v]=(0,n.N)(s),[w,x]=(0,h.A)(s,[v]),[y,z]=w(s),A=d.forwardRef((a,b)=>{let{name:c,min:g=0,max:h=100,step:j=1,orientation:k="horizontal",disabled:l=!1,minStepsBetweenThumbs:m=0,defaultValue:n=[g],value:r,onValueChange:s=()=>{},onValueCommit:u=()=>{},inverted:v=!1,form:w,...x}=a,z=d.useRef(new Set),A=d.useRef(0),B="horizontal"===k,[C=[],F]=(0,i.i)({prop:r,defaultProp:n,onChange:a=>{let b=[...z.current];b[A.current]?.focus(),s(a)}}),G=d.useRef(C);function H(a,b,{commit:c}={commit:!1}){let d=(String(j).split(".")[1]||"").length,f=function(a,b){let c=Math.pow(10,b);return Math.round(a*c)/c}(Math.round((a-g)/j)*j+g,d),i=(0,e.q)(f,[g,h]);F((a=[])=>{let d=function(a=[],b,c){let d=[...a];return d[c]=b,d.sort((a,b)=>a-b)}(a,i,b);if(!function(a,b){if(b>0)return Math.min(...a.slice(0,-1).map((b,c)=>a[c+1]-b))>=b;return!0}(d,m*j))return a;{A.current=d.indexOf(i);let b=String(d)!==String(a);return b&&c&&u(d),b?d:a}})}return(0,o.jsx)(y,{scope:a.__scopeSlider,name:c,disabled:l,min:g,max:h,valueIndexToChangeRef:A,thumbs:z.current,values:C,orientation:k,form:w,children:(0,o.jsx)(t.Provider,{scope:a.__scopeSlider,children:(0,o.jsx)(t.Slot,{scope:a.__scopeSlider,children:(0,o.jsx)(B?D:E,{"aria-disabled":l,"data-disabled":l?"":void 0,...x,ref:b,onPointerDown:(0,f.m)(x.onPointerDown,()=>{l||(G.current=C)}),min:g,max:h,inverted:v,onSlideStart:l?void 0:function(a){let b=function(a,b){if(1===a.length)return 0;let c=a.map(a=>Math.abs(a-b)),d=Math.min(...c);return c.indexOf(d)}(C,a);H(a,b)},onSlideMove:l?void 0:function(a){H(a,A.current)},onSlideEnd:l?void 0:function(){let a=G.current[A.current];C[A.current]!==a&&u(C)},onHomeKeyDown:()=>!l&&H(g,0,{commit:!0}),onEndKeyDown:()=>!l&&H(h,C.length-1,{commit:!0}),onStepKeyDown:({event:a,direction:b})=>{if(!l){let c=p.includes(a.key)||a.shiftKey&&q.includes(a.key),d=A.current;H(C[d]+j*(c?10:1)*b,d,{commit:!0})}}})})})})});A.displayName=s;var[B,C]=w(s,{startEdge:"left",endEdge:"right",size:"width",direction:1}),D=d.forwardRef((a,b)=>{let{min:c,max:e,dir:f,inverted:h,onSlideStart:i,onSlideMove:k,onSlideEnd:l,onStepKeyDown:m,...n}=a,[p,q]=d.useState(null),s=(0,g.s)(b,a=>q(a)),t=d.useRef(void 0),u=(0,j.jH)(f),v="ltr"===u,w=v&&!h||!v&&h;function x(a){let b=t.current||p.getBoundingClientRect(),d=P([0,b.width],w?[c,e]:[e,c]);return t.current=b,d(a-b.left)}return(0,o.jsx)(B,{scope:a.__scopeSlider,startEdge:w?"left":"right",endEdge:w?"right":"left",direction:w?1:-1,size:"width",children:(0,o.jsx)(F,{dir:u,"data-orientation":"horizontal",...n,ref:s,style:{...n.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:a=>{let b=x(a.clientX);i?.(b)},onSlideMove:a=>{let b=x(a.clientX);k?.(b)},onSlideEnd:()=>{t.current=void 0,l?.()},onStepKeyDown:a=>{let b=r[w?"from-left":"from-right"].includes(a.key);m?.({event:a,direction:b?-1:1})}})})}),E=d.forwardRef((a,b)=>{let{min:c,max:e,inverted:f,onSlideStart:h,onSlideMove:i,onSlideEnd:j,onStepKeyDown:k,...l}=a,m=d.useRef(null),n=(0,g.s)(b,m),p=d.useRef(void 0),q=!f;function s(a){let b=p.current||m.current.getBoundingClientRect(),d=P([0,b.height],q?[e,c]:[c,e]);return p.current=b,d(a-b.top)}return(0,o.jsx)(B,{scope:a.__scopeSlider,startEdge:q?"bottom":"top",endEdge:q?"top":"bottom",size:"height",direction:q?1:-1,children:(0,o.jsx)(F,{"data-orientation":"vertical",...l,ref:n,style:{...l.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:a=>{let b=s(a.clientY);h?.(b)},onSlideMove:a=>{let b=s(a.clientY);i?.(b)},onSlideEnd:()=>{p.current=void 0,j?.()},onStepKeyDown:a=>{let b=r[q?"from-bottom":"from-top"].includes(a.key);k?.({event:a,direction:b?-1:1})}})})}),F=d.forwardRef((a,b)=>{let{__scopeSlider:c,onSlideStart:d,onSlideMove:e,onSlideEnd:g,onHomeKeyDown:h,onEndKeyDown:i,onStepKeyDown:j,...k}=a,l=z(s,c);return(0,o.jsx)(m.sG.span,{...k,ref:b,onKeyDown:(0,f.m)(a.onKeyDown,a=>{"Home"===a.key?(h(a),a.preventDefault()):"End"===a.key?(i(a),a.preventDefault()):p.concat(q).includes(a.key)&&(j(a),a.preventDefault())}),onPointerDown:(0,f.m)(a.onPointerDown,a=>{let b=a.target;b.setPointerCapture(a.pointerId),a.preventDefault(),l.thumbs.has(b)?b.focus():d(a)}),onPointerMove:(0,f.m)(a.onPointerMove,a=>{a.target.hasPointerCapture(a.pointerId)&&e(a)}),onPointerUp:(0,f.m)(a.onPointerUp,a=>{let b=a.target;b.hasPointerCapture(a.pointerId)&&(b.releasePointerCapture(a.pointerId),g(a))})})}),G="SliderTrack",H=d.forwardRef((a,b)=>{let{__scopeSlider:c,...d}=a,e=z(G,c);return(0,o.jsx)(m.sG.span,{"data-disabled":e.disabled?"":void 0,"data-orientation":e.orientation,...d,ref:b})});H.displayName=G;var I="SliderRange",J=d.forwardRef((a,b)=>{let{__scopeSlider:c,...e}=a,f=z(I,c),h=C(I,c),i=d.useRef(null),j=(0,g.s)(b,i),k=f.values.length,l=f.values.map(a=>O(a,f.min,f.max)),n=k>1?Math.min(...l):0,p=100-Math.max(...l);return(0,o.jsx)(m.sG.span,{"data-orientation":f.orientation,"data-disabled":f.disabled?"":void 0,...e,ref:j,style:{...a.style,[h.startEdge]:n+"%",[h.endEdge]:p+"%"}})});J.displayName=I;var K="SliderThumb",L=d.forwardRef((a,b)=>{let c=u(a.__scopeSlider),[e,f]=d.useState(null),h=(0,g.s)(b,a=>f(a)),i=d.useMemo(()=>e?c().findIndex(a=>a.ref.current===e):-1,[c,e]);return(0,o.jsx)(M,{...a,ref:h,index:i})}),M=d.forwardRef((a,b)=>{let{__scopeSlider:c,index:e,name:h,...i}=a,j=z(K,c),k=C(K,c),[n,p]=d.useState(null),q=(0,g.s)(b,a=>p(a)),r=!n||j.form||!!n.closest("form"),s=(0,l.X)(n),u=j.values[e],v=void 0===u?0:O(u,j.min,j.max),w=function(a,b){return b>2?`Value ${a+1} of ${b}`:2===b?["Minimum","Maximum"][a]:void 0}(e,j.values.length),x=s?.[k.size],y=x?function(a,b,c){let d=a/2,e=P([0,50],[0,d]);return(d-e(b)*c)*c}(x,v,k.direction):0;return d.useEffect(()=>{if(n)return j.thumbs.add(n),()=>{j.thumbs.delete(n)}},[n,j.thumbs]),(0,o.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[k.startEdge]:`calc(${v}% + ${y}px)`},children:[(0,o.jsx)(t.ItemSlot,{scope:a.__scopeSlider,children:(0,o.jsx)(m.sG.span,{role:"slider","aria-label":a["aria-label"]||w,"aria-valuemin":j.min,"aria-valuenow":u,"aria-valuemax":j.max,"aria-orientation":j.orientation,"data-orientation":j.orientation,"data-disabled":j.disabled?"":void 0,tabIndex:j.disabled?void 0:0,...i,ref:q,style:void 0===u?{display:"none"}:a.style,onFocus:(0,f.m)(a.onFocus,()=>{j.valueIndexToChangeRef.current=e})})}),r&&(0,o.jsx)(N,{name:h??(j.name?j.name+(j.values.length>1?"[]":""):void 0),form:j.form,value:u},e)]})});L.displayName=K;var N=d.forwardRef(({__scopeSlider:a,value:b,...c},e)=>{let f=d.useRef(null),h=(0,g.s)(f,e),i=(0,k.Z)(b);return d.useEffect(()=>{let a=f.current;if(!a)return;let c=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(i!==b&&c){let d=new Event("input",{bubbles:!0});c.call(a,b),a.dispatchEvent(d)}},[i,b]),(0,o.jsx)(m.sG.input,{style:{display:"none"},...c,ref:h,defaultValue:b})});function O(a,b,c){return(0,e.q)(100/(c-b)*(a-b),[0,100])}function P(a,b){return c=>{if(a[0]===a[1]||b[0]===b[1])return b[0];let d=(b[1]-b[0])/(a[1]-a[0]);return b[0]+d*(c-a[0])}}N.displayName="RadioBubbleInput";var Q=A,R=H,S=J,T=L},91147:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return i},getImageProps:function(){return h}});let d=c(79809),e=c(16675),f=c(4091),g=d._(c(54963));function h(a){let{props:b}=(0,e.getImgProps)(a,{defaultLoader:g.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let i=f.Image},94199:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])}};