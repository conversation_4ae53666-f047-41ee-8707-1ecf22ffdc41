(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10529:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(71737).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11982:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(52865),e=c(99014),f=c(25590),g=c(39543),h=c(67309),i=c(43349),j=c(70108),k=c(11261),l=c(89527),m=c(34275),n=c(12292),o=c(64738),p=c(55935),q=c(261),r=c(32710),s=c(68923),t=c(26713),u=c(88055),v=c(54084),w=c(28312),x=c(48249),y=c(67455),z=c(6085),A=c(86439),B=c(27419),C=c.n(B),D=c(5031),E=c(76950),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,71385)),"D:\\Vicky\\project baru\\kost\\client\\app\\page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,18729))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,9954)),"D:\\Vicky\\project baru\\kost\\client\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,27419,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,56211,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90998,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,99233,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,18729))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\Vicky\\project baru\\kost\\client\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},13012:(a,b,c)=>{Promise.resolve().then(c.bind(c,39619))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},39619:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>E});var d=c(21157),e=c(43616),f=c(90574),g=c(56357),h=c(50634),i=c(68307),j=c(16635),k=c(38163),l=c(88358),m=c(8932),n=c(69814),o=c(10529);let p=[{icon:i.A,value:"10,000+",label:"Pengguna Aktif"},{icon:j.A,value:"500+",label:"Kost Terdaftar"},{icon:k.A,value:"4.8",label:"Rating Rata-rata"},{icon:l.A,value:"100%",label:"Terverifikasi"}],q=[{icon:m.A,title:"Preview Dinamis",description:"Lihat detail kost dengan preview interaktif dan carousel gambar"},{icon:n.A,title:"Perbandingan Mudah",description:"Bandingkan hingga 3 kost sekaligus dengan tabel perbandingan detail"},{icon:o.A,title:"Terverifikasi",description:"Semua kost telah diverifikasi untuk memastikan kualitas dan keamanan"}],r=["Jakarta Selatan","Bandung","Yogyakarta","Surabaya","Malang","Semarang"];function s({onSearch:a}){return(0,d.jsxs)("section",{className:"relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 hero-gradient opacity-90"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,d.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,d.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")"}})}),(0,d.jsx)("div",{className:"relative container mx-auto px-4 py-20 lg:py-32",children:(0,d.jsxs)("div",{className:"text-center space-y-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(g.E,{variant:"secondary",className:"bg-white/20 text-white border-white/30",children:"\uD83C\uDFE0 Platform Pencarian Kost Terdepan"}),(0,d.jsxs)("h1",{className:"text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight",children:["Temukan Kost",(0,d.jsx)("br",{}),(0,d.jsx)("span",{className:"text-yellow-300",children:"Impian Anda"})]}),(0,d.jsx)("p",{className:"text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed",children:"Platform inovatif dengan preview dinamis dan fitur perbandingan interaktif untuk membantu Anda menemukan tempat tinggal yang sempurna."})]}),(0,d.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,d.jsx)(h.I,{onSearch:a,placeholder:"Cari berdasarkan lokasi, nama kost, atau fasilitas...",className:"bg-white/95 backdrop-blur-sm rounded-2xl p-2 shadow-2xl"})}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("p",{className:"text-white/80 text-sm font-medium",children:"Lokasi Populer:"}),(0,d.jsx)("div",{className:"flex flex-wrap justify-center gap-2",children:r.map(b=>(0,d.jsxs)(f.$,{variant:"outline",size:"sm",className:"bg-white/10 border-white/30 text-white hover:bg-white/20 hover:text-white transition-all duration-300",onClick:()=>{a("",{location:b,type:"semua",priceRange:[5e5,5e6],facilities:[],sortBy:"relevance"})},children:[(0,d.jsx)(j.A,{className:"h-3 w-3 mr-1"}),b]},b))})]}),(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto pt-8",children:p.map((a,b)=>(0,d.jsxs)("div",{className:"text-center space-y-2",children:[(0,d.jsx)("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-white/20 rounded-full mb-2",children:(0,d.jsx)(a.icon,{className:"h-6 w-6 text-white"})}),(0,d.jsx)("div",{className:"text-2xl md:text-3xl font-bold text-white",children:a.value}),(0,d.jsx)("div",{className:"text-sm text-white/80",children:a.label})]},b))})]})}),(0,d.jsx)("div",{className:"relative bg-white/5 backdrop-blur-sm border-t border-white/10",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Mengapa Memilih KostHub?"}),(0,d.jsx)("p",{className:"text-white/80 text-lg max-w-2xl mx-auto",children:"Fitur-fitur inovatif yang memudahkan pencarian kost impian Anda"})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-3 gap-8 max-w-5xl mx-auto",children:q.map((a,b)=>(0,d.jsxs)("div",{className:"text-center space-y-4 group",children:[(0,d.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-2xl mb-4 group-hover:bg-white/20 transition-colors duration-300",children:(0,d.jsx)(a.icon,{className:"h-8 w-8 text-white"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-white",children:a.title}),(0,d.jsx)("p",{className:"text-white/80 leading-relaxed",children:a.description})]},b))}),(0,d.jsx)("div",{className:"text-center mt-12",children:(0,d.jsx)(f.$,{size:"lg",className:"bg-white text-primary hover:bg-white/90 font-semibold px-8 py-3 text-lg",onClick:()=>{document.getElementById("kost-listings")?.scrollIntoView({behavior:"smooth"})},children:"Jelajahi Kost Sekarang"})})]})})]})}var t=c(6062),u=c(18223);function v(){return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"space-y-4",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,d.jsx)(u.E,{className:"h-8 w-3/4"}),(0,d.jsx)(u.E,{className:"h-4 w-1/2"}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)(u.E,{className:"h-4 w-24"}),(0,d.jsx)(u.E,{className:"h-6 w-20"})]})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(u.E,{className:"h-9 w-20"}),(0,d.jsx)(u.E,{className:"h-9 w-20"})]})]})}),(0,d.jsx)(u.E,{className:"aspect-[16/9] w-full rounded-lg"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"flex space-x-1 bg-muted p-1 rounded-lg",children:Array.from({length:4}).map((a,b)=>(0,d.jsx)(u.E,{className:"h-9 flex-1"},b))}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(u.E,{className:"h-4 w-full"}),(0,d.jsx)(u.E,{className:"h-4 w-3/4"}),(0,d.jsx)(u.E,{className:"h-4 w-1/2"})]})]})]})}c(80832);var w=c(96220),x=c(52945),y=c(39401),z=c(49679);let A=(0,e.lazy)(()=>Promise.all([c.e(366),c.e(851)]).then(c.bind(c,95851)).then(a=>({default:a.KostPreviewDialog}))),B=(0,e.lazy)(()=>Promise.all([c.e(888),c.e(409),c.e(962)]).then(c.bind(c,77409)).then(a=>({default:a.ComparisonDialog}))),C=[{id:"1",title:"Kost Melati Residence",location:"Kemang, Jakarta Selatan",price:25e5,rating:4.8,reviewCount:124,images:[w.Y$.kost.room1,w.Y$.kost.interior1,w.Y$.kost.interior2],facilities:["WiFi","Parkir","Dapur","Listrik","Air","Keamanan"],type:"putri",available:3,description:"Kost nyaman dan strategis di kawasan Kemang dengan fasilitas lengkap dan keamanan 24 jam.",isWishlisted:!1},{id:"2",title:"Griya Mahasiswa Bandung",location:"Dago, Bandung",price:18e5,rating:4.6,reviewCount:89,images:[w.Y$.kost.room2,w.Y$.kost.interior3],facilities:["WiFi","Dapur","Listrik","Air","Ruang Tamu"],type:"putra",available:5,description:"Kost khusus mahasiswa dengan suasana yang kondusif untuk belajar dan dekat dengan kampus.",isWishlisted:!0},{id:"3",title:"Kost Harmoni Yogya",location:"Malioboro, Yogyakarta",price:15e5,rating:4.7,reviewCount:156,images:[w.Y$.kost.room3],facilities:["WiFi","Parkir","Listrik","Air","Keamanan","AC"],type:"campur",available:2,description:"Kost dengan lokasi strategis di jantung kota Yogyakarta, dekat dengan berbagai fasilitas umum.",isWishlisted:!1}],D=[{name:"Sarah Putri",location:"Jakarta",rating:5,comment:"Platform yang sangat membantu! Fitur perbandingan kostnya sangat berguna untuk memilih kost yang tepat.",avatar:"/avatar-1.jpg"},{name:"Ahmad Rizki",location:"Bandung",rating:5,comment:"Preview dinamis membuat saya bisa melihat detail kost dengan jelas sebelum memutuskan. Recommended!",avatar:"/avatar-2.jpg"},{name:"Dina Maharani",location:"Yogyakarta",rating:4,comment:"Pencarian kost jadi lebih mudah dan cepat. Interface yang user-friendly dan informatif.",avatar:"/avatar-3.jpg"}];function E(){let[a,b]=(0,e.useState)(null),[c,h]=(0,e.useState)(!1),[m,o]=(0,e.useState)([]),[p,q]=(0,e.useState)(!1),[r,u]=(0,e.useState)(["2"]),w=a=>{b(a),h(!0)},E=a=>{u(b=>b.includes(a)?b.filter(b=>b!==a):[...b,a])},F=a=>{let b=C.find(b=>b.id===a);b&&o(c=>c.some(b=>b.id===a)?c.filter(b=>b.id!==a):c.length<3?[...c,b]:[b,...c.slice(1)])};return(0,d.jsxs)("div",{className:"min-h-screen",children:[(0,d.jsx)(s,{onSearch:(a,b)=>{console.log("Search:",a,b)}}),(0,d.jsx)("section",{id:"kost-listings",className:"py-16 bg-background",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsxs)(g.E,{variant:"outline",className:"mb-4",children:[(0,d.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Kost Terpopuler"]}),(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Kost Pilihan Terbaik"}),(0,d.jsx)("p",{className:"text-muted-foreground text-lg max-w-2xl mx-auto",children:"Temukan kost berkualitas tinggi yang telah dipilih khusus untuk Anda"})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12",children:C.map(a=>(0,d.jsx)(t.y,{kost:{...a,isWishlisted:r.includes(a.id)},onPreview:w,onWishlist:E,onCompare:F,isComparing:m.some(b=>b.id===a.id)},a.id))}),(0,d.jsx)("div",{className:"text-center",children:(0,d.jsxs)(f.$,{size:"lg",variant:"outline",children:["Lihat Semua Kost",(0,d.jsx)(y.A,{className:"h-4 w-4 ml-2"})]})})]})}),(0,d.jsx)(x.w,{}),(0,d.jsx)("section",{className:"py-16 bg-muted/30",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsxs)(g.E,{variant:"outline",className:"mb-4",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Testimoni Pengguna"]}),(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Apa Kata Mereka?"}),(0,d.jsx)("p",{className:"text-muted-foreground text-lg max-w-2xl mx-auto",children:"Pengalaman nyata dari pengguna KostHub yang telah menemukan kost impian mereka"})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-3 gap-6",children:D.map((a,b)=>(0,d.jsxs)("div",{className:"bg-card p-6 rounded-lg border",children:[(0,d.jsx)("div",{className:"flex items-center gap-1 mb-4",children:Array.from({length:a.rating}).map((a,b)=>(0,d.jsx)(k.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"},b))}),(0,d.jsxs)("p",{className:"text-muted-foreground mb-4 leading-relaxed",children:['"',a.comment,'"']}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-muted rounded-full flex items-center justify-center",children:(0,d.jsx)(i.A,{className:"h-5 w-5"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,d.jsx)(j.A,{className:"h-3 w-3"}),a.location]})]})]})]},b))})]})}),(0,d.jsx)(x.w,{}),(0,d.jsx)("section",{className:"py-16 bg-background",children:(0,d.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,d.jsxs)("div",{className:"max-w-3xl mx-auto space-y-6",children:[(0,d.jsxs)(g.E,{variant:"outline",className:"mb-4",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Bergabung Sekarang"]}),(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold",children:"Siap Menemukan Kost Impian Anda?"}),(0,d.jsx)("p",{className:"text-muted-foreground text-lg",children:"Bergabunglah dengan ribuan pengguna yang telah menemukan tempat tinggal ideal mereka melalui KostHub"}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsxs)(f.$,{size:"lg",className:"px-8",children:[(0,d.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Mulai Pencarian"]}),(0,d.jsx)(f.$,{size:"lg",variant:"outline",className:"px-8",children:"Daftarkan Kost Anda"})]})]})})}),m.length>0&&(0,d.jsx)("div",{className:"fixed bottom-6 right-6 z-50",children:(0,d.jsxs)(f.$,{onClick:()=>q(!0),className:"rounded-full shadow-lg",size:"lg",children:["Bandingkan (",m.length,")"]})}),(0,d.jsxs)(e.Suspense,{fallback:(0,d.jsx)(v,{}),children:[(0,d.jsx)(A,{kost:a,isOpen:c,onClose:()=>h(!1),onWishlist:E,onCompare:F,isComparing:!!a&&m.some(b=>b.id===a.id)}),(0,d.jsx)(B,{kosts:m,isOpen:p,onClose:()=>q(!1),onRemoveFromComparison:a=>{o(b=>b.filter(b=>b.id!==a))}})]})]})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69814:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(71737).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},71385:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(58999).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Vicky\\\\project baru\\\\kost\\\\client\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Vicky\\project baru\\kost\\client\\app\\page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94452:(a,b,c)=>{Promise.resolve().then(c.bind(c,71385))}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[795,384,927,52,470,823],()=>b(b.s=11982));module.exports=c})();