#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Vicky/project baru/kost/client/node_modules/semver/bin/node_modules:/mnt/d/Vicky/project baru/kost/client/node_modules/semver/node_modules:/mnt/d/Vicky/project baru/kost/client/node_modules:/mnt/d/Vicky/project baru/kost/node_modules:/mnt/d/Vicky/project baru/node_modules:/mnt/d/Vicky/node_modules:/mnt/d/node_modules:/mnt/d/Vicky/project baru/kost/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/<PERSON>/project baru/kost/client/node_modules/semver/bin/node_modules:/mnt/d/Vicky/project baru/kost/client/node_modules/semver/node_modules:/mnt/d/Vicky/project baru/kost/client/node_modules:/mnt/d/Vicky/project baru/kost/node_modules:/mnt/d/Vicky/project baru/node_modules:/mnt/d/Vicky/node_modules:/mnt/d/node_modules:/mnt/d/Vicky/project baru/kost/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../semver/bin/semver.js" "$@"
else
  exec node  "$basedir/../semver/bin/semver.js" "$@"
fi
