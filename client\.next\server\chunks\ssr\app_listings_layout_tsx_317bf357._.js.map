{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/app/listings/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\"\n\nexport const metadata: Metadata = {\n  title: \"Pencarian Kost - KostHub\",\n  description: \"Temukan kost terbaik dengan fitur pencarian canggih, filter detail, dan perbandingan interaktif. Ribuan pilihan kost terverifikasi di seluruh Indonesia.\",\n  keywords: [\"pencarian kost\", \"daftar kost\", \"kost terdekat\", \"sewa kamar\", \"boarding house\"],\n  openGraph: {\n    title: \"Pencarian Kost - KostHub\",\n    description: \"Temukan kost terbaik dengan fitur pencarian canggih dan filter detail.\",\n    images: [\"/og-listings.jpg\"],\n  },\n}\n\nexport default function ListingsLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return children\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAkB;QAAe;QAAiB;QAAc;KAAiB;IAC5F,WAAW;QACT,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAmB;IAC9B;AACF;AAEe,SAAS,eAAe,EACrC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}