"use client"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { 
  Target, 
  Eye, 
  Heart, 
  Users, 
  Shield, 
  Zap,
  TrendingUp,
  CheckCircle,
  Star,
  Award
} from "lucide-react"

const features = [
  {
    icon: Zap,
    title: "Preview Dinamis",
    description: "Lihat detail kost dengan preview interaktif, carousel gambar, dan informasi lengkap sebelum memutuskan."
  },
  {
    icon: TrendingUp,
    title: "Perbandingan Mudah",
    description: "Bandingkan hingga 3 kost sekaligus dengan tabel perbandingan detail untuk membantu keputusan Anda."
  },
  {
    icon: Shield,
    title: "Terverifikasi",
    description: "Semua kost telah diverifikasi untuk memastikan kualitas, keamanan, dan kesesuaian informasi."
  },
  {
    icon: Users,
    title: "Komunitas Terpercaya",
    description: "Bergabung dengan ribuan pengguna yang telah menemukan tempat tinggal ideal melalui platform kami."
  }
]

const stats = [
  { icon: Users, value: "10,000+", label: "Pengguna Aktif" },
  { icon: CheckCircle, value: "500+", label: "Kost Terdaftar" },
  { icon: Star, value: "4.8", label: "Rating Rata-rata" },
  { icon: Award, value: "98%", label: "Tingkat Kepuasan" }
]

const team = [
  {
    name: "Ahmad Rizki",
    role: "CEO & Founder",
    description: "Berpengalaman 10+ tahun di industri properti dan teknologi."
  },
  {
    name: "Sarah Putri",
    role: "CTO",
    description: "Expert dalam pengembangan platform digital dan user experience."
  },
  {
    name: "Dina Maharani",
    role: "Head of Operations",
    description: "Spesialis dalam operasional bisnis dan customer relationship."
  }
]

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary/10 via-background to-accent/10">
        <div className="container mx-auto px-4 text-center">
          <Badge variant="outline" className="mb-6">
            <Heart className="h-4 w-4 mr-2" />
            Tentang KostHub
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Revolusi Pencarian Kost
            <br />
            <span className="text-primary">di Indonesia</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            KostHub hadir untuk mengubah cara Anda mencari dan menemukan kost impian. 
            Dengan teknologi inovatif dan fitur-fitur canggih, kami membuat proses pencarian 
            kost menjadi lebih mudah, cepat, dan menyenangkan.
          </p>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 max-w-6xl mx-auto">
            <Card className="p-8">
              <CardHeader className="text-center pb-6">
                <Target className="h-12 w-12 text-primary mx-auto mb-4" />
                <CardTitle className="text-2xl">Misi Kami</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed text-center">
                  Menyediakan platform pencarian kost yang inovatif, terpercaya, dan mudah digunakan 
                  untuk membantu setiap orang menemukan tempat tinggal yang sesuai dengan kebutuhan 
                  dan budget mereka.
                </p>
              </CardContent>
            </Card>

            <Card className="p-8">
              <CardHeader className="text-center pb-6">
                <Eye className="h-12 w-12 text-primary mx-auto mb-4" />
                <CardTitle className="text-2xl">Visi Kami</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed text-center">
                  Menjadi platform pencarian kost terdepan di Indonesia yang menghubungkan 
                  pencari kost dengan penyedia kost melalui teknologi yang user-friendly 
                  dan fitur-fitur inovatif.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Separator />

      {/* Features Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Mengapa Memilih KostHub?
            </h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Fitur-fitur inovatif yang membedakan kami dari platform pencarian kost lainnya
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <feature.icon className="h-12 w-12 text-primary mx-auto mb-4" />
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Separator />

      {/* Stats Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Pencapaian Kami
            </h2>
            <p className="text-muted-foreground text-lg">
              Angka-angka yang menunjukkan kepercayaan pengguna terhadap KostHub
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
                  <stat.icon className="h-8 w-8 text-primary" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                  {stat.value}
                </div>
                <div className="text-muted-foreground">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Separator />

      {/* Team Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Tim Kami
            </h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Orang-orang berpengalaman yang berdedikasi untuk memberikan layanan terbaik
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {team.map((member, index) => (
              <Card key={index} className="text-center p-6">
                <CardHeader>
                  <div className="w-20 h-20 bg-primary/10 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <Users className="h-10 w-10 text-primary" />
                  </div>
                  <CardTitle className="text-xl">{member.name}</CardTitle>
                  <p className="text-primary font-medium">{member.role}</p>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    {member.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto space-y-6">
            <h2 className="text-3xl md:text-4xl font-bold">
              Siap Bergabung dengan KostHub?
            </h2>
            <p className="text-muted-foreground text-lg">
              Mulai pencarian kost impian Anda hari ini dan rasakan pengalaman yang berbeda
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="px-8">
                Mulai Pencarian
              </Button>
              <Button size="lg" variant="outline" className="px-8">
                Hubungi Kami
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
