"use client"

import React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  MapPin, 
  Star, 
  X,
  Wifi, 
  Car, 
  Utensils, 
  Zap, 
  Droplets, 
  Shield, 
  Users,
  Check,
  Minus
} from "lucide-react"
import { KostData } from "./kost-card"
import { cn } from "@/lib/utils"
import { DEFAULT_KOST_IMAGE } from "@/lib/images"
import Image from "next/image"

interface ComparisonDialogProps {
  kosts: KostData[]
  isOpen: boolean
  onClose: () => void
  onRemoveFromComparison: (kostId: string) => void
}

const facilityIcons: Record<string, React.ComponentType<any>> = {
  wifi: Wifi,
  parkir: Car,
  dapur: Utensils,
  listrik: Zap,
  air: Droplets,
  keamanan: Shield,
  "ruang tamu": Users,
}

const allFacilities = [
  "WiFi",
  "Parkir", 
  "Dapur",
  "Listrik",
  "Air",
  "Keamanan",
  "Ruang Tamu",
  "AC",
  "Kasur",
  "Lemari"
]

export function ComparisonDialog({ 
  kosts, 
  isOpen, 
  onClose, 
  onRemoveFromComparison 
}: ComparisonDialogProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price)
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "putra":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      case "putri":
        return "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300"
      case "campur":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  const hasFacility = (kost: KostData, facility: string) => {
    return kost.facilities.some(f => f.toLowerCase() === facility.toLowerCase())
  }

  if (kosts.length === 0) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Perbandingan Kost</DialogTitle>
          </DialogHeader>
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">
              Belum ada kost yang dipilih untuk dibandingkan.
            </p>
            <Button onClick={onClose}>Tutup</Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Perbandingan Kost ({kosts.length})</span>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="h-[calc(90vh-120px)]">
          <div className="comparison-table">
            {/* Header with Kost Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {kosts.map((kost) => (
                <div key={kost.id} className="relative bg-card border rounded-lg overflow-hidden">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-2 right-2 z-10 h-8 w-8 p-0 bg-white/90 hover:bg-white"
                    onClick={() => onRemoveFromComparison(kost.id)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                  
                  <div className="relative aspect-[4/3]">
                    <Image
                      src={kost.images[0] || DEFAULT_KOST_IMAGE}
                      alt={kost.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  </div>
                  
                  <div className="p-4">
                    <h3 className="font-semibold text-lg line-clamp-2 mb-2">
                      {kost.title}
                    </h3>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground mb-2">
                      <MapPin className="h-3 w-3" />
                      <span className="line-clamp-1">{kost.location}</span>
                    </div>
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="font-medium text-sm">{kost.rating}</span>
                      </div>
                      <Badge className={getTypeColor(kost.type)}>
                        {kost.type}
                      </Badge>
                    </div>
                    <div className="text-2xl font-bold text-primary">
                      {formatPrice(kost.price)}
                    </div>
                    <div className="text-sm text-muted-foreground">per bulan</div>
                  </div>
                </div>
              ))}
            </div>

            <Separator className="my-6" />

            {/* Comparison Table */}
            <div className="space-y-6">
              {/* Basic Information */}
              <div>
                <h3 className="font-semibold text-lg mb-4">Informasi Dasar</h3>
                <div className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="font-medium text-muted-foreground">Harga per Bulan</div>
                    {kosts.map((kost) => (
                      <div key={`price-${kost.id}`} className="font-semibold text-primary">
                        {formatPrice(kost.price)}
                      </div>
                    ))}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="font-medium text-muted-foreground">Rating</div>
                    {kosts.map((kost) => (
                      <div key={`rating-${kost.id}`} className="flex items-center gap-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="font-medium">{kost.rating}</span>
                        <span className="text-sm text-muted-foreground">
                          ({kost.reviewCount})
                        </span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="font-medium text-muted-foreground">Kamar Tersedia</div>
                    {kosts.map((kost) => (
                      <div key={`available-${kost.id}`}>
                        {kost.available} kamar
                      </div>
                    ))}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="font-medium text-muted-foreground">Tipe Kost</div>
                    {kosts.map((kost) => (
                      <div key={`type-${kost.id}`}>
                        <Badge className={getTypeColor(kost.type)}>
                          Kost {kost.type.charAt(0).toUpperCase() + kost.type.slice(1)}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <Separator />

              {/* Facilities Comparison */}
              <div>
                <h3 className="font-semibold text-lg mb-4">Perbandingan Fasilitas</h3>
                <div className="space-y-3">
                  {allFacilities.map((facility) => (
                    <div key={facility} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div className="flex items-center gap-2 font-medium text-muted-foreground">
                        {facilityIcons[facility.toLowerCase()] && (
                          React.createElement(facilityIcons[facility.toLowerCase()], {
                            className: "h-4 w-4"
                          })
                        )}
                        {facility}
                      </div>
                      {kosts.map((kost) => (
                        <div key={`${facility}-${kost.id}`} className="flex items-center">
                          {hasFacility(kost, facility) ? (
                            <div className="flex items-center gap-2 text-green-600">
                              <Check className="h-4 w-4" />
                              <span className="text-sm">Tersedia</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <Minus className="h-4 w-4" />
                              <span className="text-sm">Tidak tersedia</span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Action Buttons */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="font-medium text-muted-foreground">Aksi</div>
                {kosts.map((kost) => (
                  <div key={`action-${kost.id}`} className="space-y-2">
                    <Button className="w-full" size="sm">
                      Lihat Detail
                    </Button>
                    <Button variant="outline" className="w-full" size="sm">
                      Hubungi Pemilik
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
