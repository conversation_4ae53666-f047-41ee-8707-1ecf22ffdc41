{"version": 3, "sources": ["../../../src/client/components/unresolved-thenable.ts"], "sourcesContent": ["/**\n * Create a \"Thenable\" that does not resolve. This is used to suspend indefinitely when data is not available yet.\n */\nexport const unresolvedThenable = {\n  then: () => {},\n} as PromiseLike<void>\n"], "names": ["unresolvedThenable", "then"], "mappings": "AAAA;;CAEC;;;;+BACYA;;;eAAAA;;;AAAN,MAAMA,qBAAqB;IAChCC,MAAM,KAAO;AACf", "ignoreList": [0]}