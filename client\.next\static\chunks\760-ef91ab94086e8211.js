"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[760],{1434:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(5461);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=l(e,r)),t&&(o.current=l(t,r))},[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4046:(e,t,n)=>{n.d(t,{b:()=>s});var r=n(5461),o=n(3713),l=n(2273),a="horizontal",i=["horizontal","vertical"],u=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:u=a,...s}=e,d=(n=u,i.includes(n))?u:a;return(0,l.jsx)(o.sG.div,{"data-orientation":d,...r?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...s,ref:t})});u.displayName="Separator";var s=u},4991:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(865).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},6309:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(865).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},8453:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(5461),o=n(7239),l=n(1465),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef(null),s=r.useRef(e),d=r.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(u.current);d.current="mounted"===c?e:"none"},[c]),(0,l.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=d.current,o=i(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,l.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=i(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(d.current=i(u.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),s=(0,o.s)(a.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:s}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},9472:(e,t,n)=>{n.d(t,{UC:()=>et,ZL:()=>$,bL:()=>X,bm:()=>er,hE:()=>en,hJ:()=>ee,l9:()=>Y});var r=n(5461),o=n(582),l=n(7239),a=n(4284),i=n(5821),u=n(4976),s=n(99),d=n(9092),c=n(4820),f=n(8453),p=n(3713),m=n(3278),g=n(8571),v=n(3767),y=n(3330),N=n(2273),h="Dialog",[b,O]=(0,a.A)(h),[D,R]=b(h),j=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:l,onOpenChange:a,modal:s=!0}=e,d=r.useRef(null),c=r.useRef(null),[f,p]=(0,u.i)({prop:o,defaultProp:null!=l&&l,onChange:a,caller:h});return(0,N.jsx)(D,{scope:t,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:n})};j.displayName=h;var w="DialogTrigger",x=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=R(w,n),i=(0,l.s)(t,a.triggerRef);return(0,N.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":z(a.open),...r,ref:i,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});x.displayName=w;var C="DialogPortal",[I,A]=b(C,{forceMount:void 0}),_=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:l}=e,a=R(C,t);return(0,N.jsx)(I,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,N.jsx)(f.C,{present:n||a.open,children:(0,N.jsx)(c.Z,{asChild:!0,container:l,children:e})}))})};_.displayName=C;var E="DialogOverlay",M=r.forwardRef((e,t)=>{let n=A(E,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=R(E,e.__scopeDialog);return l.modal?(0,N.jsx)(f.C,{present:r||l.open,children:(0,N.jsx)(k,{...o,ref:t})}):null});M.displayName=E;var T=(0,y.TL)("DialogOverlay.RemoveScroll"),k=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(E,n);return(0,N.jsx)(g.A,{as:T,allowPinchZoom:!0,shards:[o.contentRef],children:(0,N.jsx)(p.sG.div,{"data-state":z(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",F=r.forwardRef((e,t)=>{let n=A(P,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=R(P,e.__scopeDialog);return(0,N.jsx)(f.C,{present:r||l.open,children:l.modal?(0,N.jsx)(U,{...o,ref:t}):(0,N.jsx)(L,{...o,ref:t})})});F.displayName=P;var U=r.forwardRef((e,t)=>{let n=R(P,e.__scopeDialog),a=r.useRef(null),i=(0,l.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,N.jsx)(S,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=r.forwardRef((e,t)=>{let n=R(P,e.__scopeDialog),o=r.useRef(!1),l=r.useRef(!1);return(0,N.jsx)(S,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(a=n.triggerRef.current)||a.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var r,a;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let i=t.target;(null==(a=n.triggerRef.current)?void 0:a.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),S=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,...u}=e,c=R(P,n),f=r.useRef(null),p=(0,l.s)(t,f);return(0,m.Oh)(),(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,N.jsx)(s.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":z(c.open),...u,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)(K,{titleId:c.titleId}),(0,N.jsx)(Q,{contentRef:f,descriptionId:c.descriptionId})]})]})}),W="DialogTitle",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(W,n);return(0,N.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});G.displayName=W;var B="DialogDescription";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(B,n);return(0,N.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})}).displayName=B;var q="DialogClose",Z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=R(q,n);return(0,N.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function z(e){return e?"open":"closed"}Z.displayName=q;var V="DialogTitleWarning",[H,J]=(0,a.q)(V,{contentName:P,titleName:W,docsSlug:"dialog"}),K=e=>{let{titleId:t}=e,n=J(V),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},Q=e=>{let{contentRef:t,descriptionId:n}=e,o=J("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(l))},[l,t,n]),null},X=j,Y=x,$=_,ee=M,et=F,en=G,er=Z},9889:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(865).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}}]);