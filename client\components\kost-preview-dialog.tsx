"use client"

import Image from "next/image"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { 
  MapPin, 
  Star, 
  Heart, 
  Share2, 
  Phone, 
  MessageCircle,
  Wifi, 
  Car, 
  Utensils, 
  Zap, 
  Droplets, 
  Shield, 
  Users,
  Bed,
  Home,
  Calendar,
  Clock
} from "lucide-react"
import { KostData } from "./kost-card"
import { cn } from "@/lib/utils"
import { UNSPLASH_IMAGES } from "@/lib/images"

interface KostPreviewDialogProps {
  kost: KostData | null
  isOpen: boolean
  onClose: () => void
  onWishlist?: (kostId: string) => void
  onCompare?: (kostId: string) => void
  isComparing?: boolean
}

const facilityIcons: Record<string, React.ComponentType<any>> = {
  wifi: Wifi,
  parkir: Car,
  dapur: Utensils,
  listrik: Zap,
  air: Droplets,
  keamanan: Shield,
  "ruang tamu": Users,
  ac: Zap,
  kasur: Bed,
  lemari: Home,
}

// Mock data untuk review dan detail tambahan
const mockReviews = [
  {
    id: "1",
    user: "Andi Pratama",
    rating: 5,
    comment: "Kost yang sangat nyaman dan bersih. Pemilik ramah dan fasilitas lengkap.",
    date: "2024-01-15",
    avatar: UNSPLASH_IMAGES.avatars.male2
  },
  {
    id: "2",
    user: "Sari Dewi",
    rating: 4,
    comment: "Lokasi strategis dekat kampus. WiFi cepat dan kamar luas.",
    date: "2024-01-10",
    avatar: UNSPLASH_IMAGES.avatars.female3
  },
  {
    id: "3",
    user: "Budi Santoso",
    rating: 5,
    comment: "Pelayanan excellent! Kost bersih dan aman. Highly recommended untuk mahasiswa.",
    date: "2024-01-08",
    avatar: UNSPLASH_IMAGES.avatars.male5
  }
]

const mockRules = [
  "Jam malam pukul 22.00 WIB",
  "Dilarang membawa tamu menginap",
  "Dilarang merokok di dalam kamar",
  "Wajib menjaga kebersihan bersama",
  "Pembayaran dilakukan setiap tanggal 1"
]

export function KostPreviewDialog({ 
  kost, 
  isOpen, 
  onClose, 
  onWishlist, 
  onCompare,
  isComparing = false 
}: KostPreviewDialogProps) {
  if (!kost) return null

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price)
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "putra":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      case "putri":
        return "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300"
      case "campur":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto custom-scrollbar">
        <DialogHeader className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <DialogTitle className="text-2xl font-bold">{kost.title}</DialogTitle>
              <div className="flex items-center gap-2 text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>{kost.location}</span>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium">{kost.rating}</span>
                  <span className="text-muted-foreground">({kost.reviewCount} ulasan)</span>
                </div>
                <Badge className={getTypeColor(kost.type)}>
                  Kost {kost.type.charAt(0).toUpperCase() + kost.type.slice(1)}
                </Badge>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onWishlist?.(kost.id)}
              >
                <Heart 
                  className={cn(
                    "h-4 w-4 mr-2",
                    kost.isWishlisted ? "fill-red-500 text-red-500" : ""
                  )} 
                />
                {kost.isWishlisted ? "Tersimpan" : "Simpan"}
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Bagikan
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Image Carousel */}
          <div className="relative">
            <Carousel className="w-full">
              <CarouselContent>
                {kost.images.map((image, index) => (
                  <CarouselItem key={index}>
                    <div className="relative aspect-[16/9] overflow-hidden rounded-lg">
                      <Image
                        src={image}
                        alt={`${kost.title} - Gambar ${index + 1}`}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, 800px"
                      />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="left-4" />
              <CarouselNext className="right-4" />
            </Carousel>
          </div>

          {/* Content Tabs */}
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Ringkasan</TabsTrigger>
              <TabsTrigger value="facilities">Fasilitas</TabsTrigger>
              <TabsTrigger value="reviews">Ulasan</TabsTrigger>
              <TabsTrigger value="rules">Peraturan</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-lg mb-2">Deskripsi</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {kost.description}
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-lg mb-2">Detail Kost</h3>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Bed className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{kost.available} kamar tersedia</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Home className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">Tipe: Kost {kost.type}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">Pembayaran bulanan</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="bg-muted p-4 rounded-lg">
                    <div className="text-3xl font-bold text-primary mb-1">
                      {formatPrice(kost.price)}
                    </div>
                    <div className="text-sm text-muted-foreground mb-4">per bulan</div>
                    
                    <div className="space-y-2">
                      <Button className="w-full" size="lg">
                        <Phone className="h-4 w-4 mr-2" />
                        Hubungi Pemilik
                      </Button>
                      <Button variant="outline" className="w-full" size="lg">
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Chat WhatsApp
                      </Button>
                      {onCompare && (
                        <Button 
                          variant={isComparing ? "default" : "outline"} 
                          className="w-full" 
                          size="lg"
                          onClick={() => onCompare(kost.id)}
                        >
                          {isComparing ? "Terpilih untuk Perbandingan" : "Bandingkan Kost"}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="facilities" className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {kost.facilities.map((facility) => {
                  const IconComponent = facilityIcons[facility.toLowerCase()] || Shield
                  return (
                    <div
                      key={facility}
                      className="flex items-center gap-3 p-3 bg-muted rounded-lg"
                    >
                      <IconComponent className="h-5 w-5 text-primary" />
                      <span className="font-medium">{facility}</span>
                    </div>
                  )
                })}
              </div>
            </TabsContent>
            
            <TabsContent value="reviews" className="space-y-4">
              <div className="space-y-4">
                {mockReviews.map((review) => (
                  <div key={review.id} className="border rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <div className="relative w-10 h-10 rounded-full overflow-hidden flex-shrink-0">
                        <Image
                          src={review.avatar}
                          alt={`${review.user} avatar`}
                          fill
                          className="object-cover"
                          sizes="40px"
                        />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">{review.user}</span>
                          <div className="flex items-center gap-1">
                            {Array.from({ length: review.rating }).map((_, i) => (
                              <Star key={i} className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                            ))}
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{review.comment}</p>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>{new Date(review.date).toLocaleDateString('id-ID')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="rules" className="space-y-4">
              <div className="space-y-3">
                {mockRules.map((rule, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-muted rounded-lg">
                    <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
                      {index + 1}
                    </div>
                    <span>{rule}</span>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  )
}
