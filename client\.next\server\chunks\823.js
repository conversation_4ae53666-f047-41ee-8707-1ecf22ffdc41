"use strict";exports.id=823,exports.ids=[823],exports.modules={6062:(a,b,c)=>{c.d(b,{y:()=>x});var d=c(21157),e=c(70696),f=c(56357),g=c(90574),h=c(80832),i=c(52945),j=c(89122),k=c(45817),l=c(71960),m=c(8932),n=c(18080),o=c(88358),p=c(68307),q=c(49679),r=c(81986),s=c(16635),t=c(38163),u=c(89369),v=c(96220);let w={wifi:j.A,parkir:k.A,dapur:l.A,listrik:m.A,air:n.A,keamanan:o.A,"ruang tamu":p.A};function x({kost:a,onPreview:b,onWishlist:c,onCompare:j,isComparing:k=!1,className:l}){var m;return(0,d.jsxs)(h.Zp,{className:(0,u.cn)("kost-card group overflow-hidden",l),children:[(0,d.jsx)(h.aR,{className:"p-0 relative",children:(0,d.jsxs)("div",{className:"relative aspect-[4/3] overflow-hidden",children:[(0,d.jsx)(e.default,{src:a.images[0]||v.wf,alt:a.title,fill:!0,className:"kost-card-image object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300",children:(0,d.jsxs)("div",{className:"absolute top-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[(0,d.jsx)(g.$,{size:"sm",variant:"secondary",className:"h-8 w-8 p-0 bg-white/90 hover:bg-white",onClick:()=>c?.(a.id),children:(0,d.jsx)(q.A,{className:(0,u.cn)("h-4 w-4",a.isWishlisted?"fill-red-500 text-red-500":"text-gray-600")})}),(0,d.jsx)(g.$,{size:"sm",variant:"secondary",className:"h-8 w-8 p-0 bg-white/90 hover:bg-white",onClick:()=>b?.(a),children:(0,d.jsx)(r.A,{className:"h-4 w-4 text-gray-600"})})]})}),(0,d.jsx)("div",{className:"absolute top-3 left-3",children:(0,d.jsxs)(f.E,{className:(a=>{switch(a){case"putra":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";case"putri":return"bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300";case"campur":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"}})(a.type),children:["Kost ",a.type.charAt(0).toUpperCase()+a.type.slice(1)]})}),(0,d.jsx)("div",{className:"absolute bottom-3 left-3",children:(0,d.jsxs)(f.E,{variant:"secondary",className:"bg-white/90 text-gray-800",children:[a.available," kamar tersedia"]})})]})}),(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-lg line-clamp-1 group-hover:text-primary transition-colors",children:a.title}),(0,d.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground mt-1",children:[(0,d.jsx)(s.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{className:"line-clamp-1",children:a.location})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(t.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,d.jsx)("span",{className:"font-medium text-sm",children:a.rating})]}),(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",a.reviewCount," ulasan)"]})]}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2",children:[a.facilities.slice(0,3).map(a=>{let b=w[a.toLowerCase()]||o.A;return(0,d.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground bg-muted px-2 py-1 rounded-md",children:[(0,d.jsx)(b,{className:"h-3 w-3 flex-shrink-0"}),(0,d.jsx)("span",{className:"truncate",children:a})]},a)}),a.facilities.length>3&&(0,d.jsxs)("div",{className:"text-xs text-muted-foreground bg-muted px-2 py-1 rounded-md",children:["+",a.facilities.length-3," lainnya"]})]})]})}),(0,d.jsx)(i.w,{}),(0,d.jsx)(h.wL,{className:"p-4 pt-3",children:(0,d.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary",children:(m=a.price,new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0,maximumFractionDigits:0}).format(m))}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:"per bulan"})]}),(0,d.jsxs)("div",{className:"flex gap-2 flex-col sm:flex-row",children:[j&&(0,d.jsx)(g.$,{variant:k?"default":"outline",size:"sm",onClick:()=>j(a.id),className:"flex-1 sm:flex-none",children:k?"Terpilih":"Bandingkan"}),(0,d.jsx)(g.$,{size:"sm",onClick:()=>b?.(a),className:"flex-1 sm:flex-none",children:"Lihat Detail"})]})]})})]})}},18223:(a,b,c)=>{c.d(b,{E:()=>f});var d=c(21157),e=c(89369);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"skeleton",className:(0,e.cn)("bg-accent animate-pulse rounded-md",a),...b})}},50634:(a,b,c)=>{c.d(b,{I:()=>t});var d=c(21157),e=c(43616),f=c(90574),g=c(94964),h=c(94750),i=c(91123),j=c(89369);function k({className:a,defaultValue:b,value:c,min:f=0,max:g=100,...h}){let k=e.useMemo(()=>Array.isArray(c)?c:Array.isArray(b)?b:[f,g],[c,b,f,g]);return(0,d.jsxs)(i.bL,{"data-slot":"slider",defaultValue:b,value:c,min:f,max:g,className:(0,j.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",a),...h,children:[(0,d.jsx)(i.CC,{"data-slot":"slider-track",className:(0,j.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,d.jsx)(i.Q6,{"data-slot":"slider-range",className:(0,j.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:k.length},(a,b)=>(0,d.jsx)(i.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},b))]})}var l=c(56357),m=c(72057),n=c(83343),o=c(94199),p=c(44939),q=c(16635);let r=["WiFi","Parkir","Dapur","Listrik","Air","Keamanan","Ruang Tamu","AC","Kasur","Lemari"],s=["Semua Lokasi","Jakarta Pusat","Jakarta Selatan","Jakarta Barat","Jakarta Utara","Jakarta Timur","Bandung","Surabaya","Yogyakarta","Semarang","Malang"];function t({onSearch:a,className:b,placeholder:c="Cari kost berdasarkan lokasi, nama, atau fasilitas...",showFilters:i=!0}){let[t,u]=(0,e.useState)(""),[v,w]=(0,e.useState)({location:"",type:"semua",priceRange:[5e5,5e6],facilities:[],sortBy:"relevance"}),[x,y]=(0,e.useState)(!1),z=()=>{a(t,v)},A=a=>{w(b=>({...b,facilities:b.facilities.includes(a)?b.facilities.filter(b=>b!==a):[...b.facilities,a]}))},B=a=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(a),C=[v.location&&""!==v.location,"semua"!==v.type,5e5!==v.priceRange[0]||5e6!==v.priceRange[1],v.facilities.length>0,"relevance"!==v.sortBy].filter(Boolean).length;return(0,d.jsxs)("div",{className:(0,j.cn)("search-bar",b),children:[(0,d.jsxs)("div",{className:"flex gap-2 w-full",children:[(0,d.jsxs)("div",{className:"relative flex-1",children:[(0,d.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{type:"text",placeholder:c,value:t,onChange:a=>u(a.target.value),onKeyPress:a=>{"Enter"===a.key&&z()},className:"pl-10 pr-4 h-12 text-base"})]}),i&&(0,d.jsxs)(m.cj,{open:x,onOpenChange:y,children:[(0,d.jsx)(m.CG,{asChild:!0,children:(0,d.jsxs)(f.$,{variant:"outline",size:"lg",className:"h-12 px-4 relative",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Filter",C>0&&(0,d.jsx)(l.E,{variant:"destructive",className:"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs",children:C})]})}),(0,d.jsxs)(m.h,{className:"w-full sm:max-w-md",children:[(0,d.jsx)(m.Fm,{children:(0,d.jsxs)(m.qp,{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:"Filter Pencarian"}),(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>{w({location:"",type:"semua",priceRange:[5e5,5e6],facilities:[],sortBy:"relevance"})},children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Reset"]})]})}),(0,d.jsxs)("div",{className:"filter-panel mt-6 space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{className:"text-sm font-medium",children:"Lokasi"}),(0,d.jsxs)(h.l6,{value:v.location,onValueChange:a=>w(b=>({...b,location:"Semua Lokasi"===a?"":a})),children:[(0,d.jsx)(h.bq,{children:(0,d.jsx)(h.yv,{placeholder:"Pilih lokasi"})}),(0,d.jsx)(h.gC,{children:s.map(a=>(0,d.jsx)(h.eb,{value:a,children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(q.A,{className:"h-4 w-4"}),a]})},a))})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{className:"text-sm font-medium",children:"Tipe Kost"}),(0,d.jsxs)(h.l6,{value:v.type,onValueChange:a=>w(b=>({...b,type:a})),children:[(0,d.jsx)(h.bq,{children:(0,d.jsx)(h.yv,{})}),(0,d.jsxs)(h.gC,{children:[(0,d.jsx)(h.eb,{value:"semua",children:"Semua Tipe"}),(0,d.jsx)(h.eb,{value:"putra",children:"Kost Putra"}),(0,d.jsx)(h.eb,{value:"putri",children:"Kost Putri"}),(0,d.jsx)(h.eb,{value:"campur",children:"Kost Campur"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("label",{className:"text-sm font-medium",children:"Rentang Harga"}),(0,d.jsx)("div",{className:"px-2",children:(0,d.jsx)(k,{value:v.priceRange,onValueChange:a=>w(b=>({...b,priceRange:a})),max:1e7,min:3e5,step:1e5,className:"w-full"})}),(0,d.jsxs)("div",{className:"price-range",children:[(0,d.jsx)("span",{children:B(v.priceRange[0])}),(0,d.jsx)("span",{children:B(v.priceRange[1])})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("label",{className:"text-sm font-medium",children:"Fasilitas"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:r.map(a=>(0,d.jsx)(l.E,{variant:v.facilities.includes(a)?"default":"outline",className:"cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors",onClick:()=>A(a),children:a},a))})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{className:"text-sm font-medium",children:"Urutkan"}),(0,d.jsxs)(h.l6,{value:v.sortBy,onValueChange:a=>w(b=>({...b,sortBy:a})),children:[(0,d.jsx)(h.bq,{children:(0,d.jsx)(h.yv,{})}),(0,d.jsxs)(h.gC,{children:[(0,d.jsx)(h.eb,{value:"relevance",children:"Paling Relevan"}),(0,d.jsx)(h.eb,{value:"price-low",children:"Harga Terendah"}),(0,d.jsx)(h.eb,{value:"price-high",children:"Harga Tertinggi"}),(0,d.jsx)(h.eb,{value:"rating",children:"Rating Tertinggi"}),(0,d.jsx)(h.eb,{value:"newest",children:"Terbaru"})]})]})]})]}),(0,d.jsx)("div",{className:"mt-6 flex gap-2",children:(0,d.jsx)(f.$,{onClick:()=>{z(),y(!1)},className:"flex-1",children:"Terapkan Filter"})})]})]}),(0,d.jsx)(f.$,{onClick:z,size:"lg",className:"h-12 px-6",children:"Cari"})]}),C>0&&(0,d.jsxs)("div",{className:"flex flex-wrap gap-2 mt-3",children:[v.location&&(0,d.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:[(0,d.jsx)(q.A,{className:"h-3 w-3"}),v.location,(0,d.jsx)(p.A,{className:"h-3 w-3 cursor-pointer hover:text-destructive",onClick:()=>w(a=>({...a,location:""}))})]}),"semua"!==v.type&&(0,d.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:["Kost ",v.type,(0,d.jsx)(p.A,{className:"h-3 w-3 cursor-pointer hover:text-destructive",onClick:()=>w(a=>({...a,type:"semua"}))})]}),v.facilities.map(a=>(0,d.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:[a,(0,d.jsx)(p.A,{className:"h-3 w-3 cursor-pointer hover:text-destructive",onClick:()=>A(a)})]},a))]})]})}},56357:(a,b,c)=>{c.d(b,{E:()=>i});var d=c(21157);c(43616);var e=c(84726),f=c(59542),g=c(89369);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}},94750:(a,b,c)=>{c.d(b,{bq:()=>l,eb:()=>n,gC:()=>m,l6:()=>j,yv:()=>k});var d=c(21157);c(43616);var e=c(7612),f=c(40909),g=c(2499),h=c(40812),i=c(89369);function j({...a}){return(0,d.jsx)(e.bL,{"data-slot":"select",...a})}function k({...a}){return(0,d.jsx)(e.WT,{"data-slot":"select-value",...a})}function l({className:a,size:b="default",children:c,...g}){return(0,d.jsxs)(e.l9,{"data-slot":"select-trigger","data-size":b,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...g,children:[c,(0,d.jsx)(e.In,{asChild:!0,children:(0,d.jsx)(f.A,{className:"size-4 opacity-50"})})]})}function m({className:a,children:b,position:c="popper",...f}){return(0,d.jsx)(e.ZL,{children:(0,d.jsxs)(e.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...f,children:[(0,d.jsx)(o,{}),(0,d.jsx)(e.LM,{className:(0,i.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:b}),(0,d.jsx)(p,{})]})})}function n({className:a,children:b,...c}){return(0,d.jsxs)(e.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...c,children:[(0,d.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,d.jsx)(e.VF,{children:(0,d.jsx)(g.A,{className:"size-4"})})}),(0,d.jsx)(e.p4,{children:b})]})}function o({className:a,...b}){return(0,d.jsx)(e.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"size-4"})})}function p({className:a,...b}){return(0,d.jsx)(e.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(f.A,{className:"size-4"})})}},94964:(a,b,c)=>{c.d(b,{p:()=>f});var d=c(21157);c(43616);var e=c(89369);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}},96220:(a,b,c)=>{c.d(b,{Y$:()=>d,wf:()=>e});let d={kost:{room1:"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop&crop=center",room2:"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop&crop=center",room3:"https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop&crop=center",room4:"https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop&crop=center",room5:"https://images.unsplash.com/photo-1493809842364-78817add7ffb?w=800&h=600&fit=crop&crop=center",room6:"https://images.unsplash.com/photo-1571624436279-b272aff752b5?w=800&h=600&fit=crop&crop=center",interior1:"https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop&crop=center",interior2:"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&crop=center",interior3:"https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop&crop=center"},avatars:{male1:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",female1:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",male2:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",female2:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",male3:"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face",female3:"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face"},og:{main:"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=1200&h=630&fit=crop&crop=center",listings:"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=1200&h=630&fit=crop&crop=center",about:"https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=1200&h=630&fit=crop&crop=center",contact:"https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=1200&h=630&fit=crop&crop=center"},buildings:{jakarta:"https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop&crop=center",bandung:"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=center",yogya:"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop&crop=center",surabaya:"https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=800&h=600&fit=crop&crop=center"},facilities:{wifi:"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center",parking:"https://images.unsplash.com/photo-1506521781263-d8422e82f27a?w=400&h=300&fit=crop&crop=center",kitchen:"https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&crop=center",security:"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center",laundry:"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center"}},e=d.kost.room1;d.kost.room1,d.kost.room2,d.kost.room3}};