{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/navigation.tsx <module evaluation>\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2DACA", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/navigation.tsx\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uCACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/footer.tsx <module evaluation>\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uDACA", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/footer.tsx\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,mCACA", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/error-boundary.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DialogErrorFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call DialogErrorFallback() from the server but DialogErrorFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/error-boundary.tsx <module evaluation>\",\n    \"DialogErrorFallback\",\n);\nexport const ErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/error-boundary.tsx <module evaluation>\",\n    \"ErrorBoundary\",\n);\nexport const KostCardErrorFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call KostCardErrorFallback() from the server but KostCardErrorFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/error-boundary.tsx <module evaluation>\",\n    \"KostCardErrorFallback\",\n);\nexport const SearchErrorFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call SearchErrorFallback() from the server but SearchErrorFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/error-boundary.tsx <module evaluation>\",\n    \"SearchErrorFallback\",\n);\nexport const useErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call useErrorBoundary() from the server but useErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/error-boundary.tsx <module evaluation>\",\n    \"useErrorBoundary\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,+DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,+DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,+DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+DACA", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/error-boundary.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DialogErrorFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call DialogErrorFallback() from the server but DialogErrorFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/error-boundary.tsx\",\n    \"DialogErrorFallback\",\n);\nexport const ErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/error-boundary.tsx\",\n    \"ErrorBoundary\",\n);\nexport const KostCardErrorFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call KostCardErrorFallback() from the server but KostCardErrorFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/error-boundary.tsx\",\n    \"KostCardErrorFallback\",\n);\nexport const SearchErrorFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call SearchErrorFallback() from the server but SearchErrorFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/error-boundary.tsx\",\n    \"SearchErrorFallback\",\n);\nexport const useErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call useErrorBoundary() from the server but useErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/error-boundary.tsx\",\n    \"useErrorBoundary\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,2CACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2CACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,2CACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,2CACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2CACA", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/lib/images.ts"], "sourcesContent": ["// Unsplash image URLs for KostHub\n// All images are optimized for web with proper dimensions and cropping\n\nexport const UNSPLASH_IMAGES = {\n  // Kost room images - modern, clean boarding house rooms\n  kost: {\n    room1: \"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop&crop=center\", // Modern bedroom\n    room2: \"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop&crop=center\", // Cozy bedroom\n    room3: \"https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop&crop=center\", // Minimalist room\n    room4: \"https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop&crop=center\", // Luxury bedroom\n    room5: \"https://images.unsplash.com/photo-1493809842364-78817add7ffb?w=800&h=600&fit=crop&crop=center\", // Simple room\n    room6: \"https://images.unsplash.com/photo-1571624436279-b272aff752b5?w=800&h=600&fit=crop&crop=center\", // Contemporary room\n    \n    // Additional room views\n    interior1: \"https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop&crop=center\", // Living area\n    interior2: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&crop=center\", // Kitchen area\n    interior3: \"https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop&crop=center\", // Study area\n  },\n  \n  // User avatars - diverse, professional headshots\n  avatars: {\n    male1: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face\", // Professional male\n    female1: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face\", // Professional female\n    male2: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face\", // Young male\n    female2: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face\", // Young female\n    male3: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face\", // Casual male\n    female3: \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face\", // Casual female\n  },\n  \n  // Open Graph images for social sharing\n  og: {\n    main: \"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=1200&h=630&fit=crop&crop=center\", // Main OG image\n    listings: \"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=1200&h=630&fit=crop&crop=center\", // Listings page\n    about: \"https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=1200&h=630&fit=crop&crop=center\", // About page\n    contact: \"https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=1200&h=630&fit=crop&crop=center\", // Contact page\n  },\n  \n  // Building exteriors for kost locations\n  buildings: {\n    jakarta: \"https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop&crop=center\", // Jakarta building\n    bandung: \"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=center\", // Bandung building\n    yogya: \"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop&crop=center\", // Yogya building\n    surabaya: \"https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=800&h=600&fit=crop&crop=center\", // Surabaya building\n  },\n  \n  // Facility images\n  facilities: {\n    wifi: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center\", // WiFi setup\n    parking: \"https://images.unsplash.com/photo-1506521781263-d8422e82f27a?w=400&h=300&fit=crop&crop=center\", // Parking area\n    kitchen: \"https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&crop=center\", // Kitchen\n    security: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center\", // Security system\n    laundry: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center\", // Laundry area\n  }\n} as const\n\n// Helper functions for getting images\nexport const getKostImage = (index: number = 0): string => {\n  const images = Object.values(UNSPLASH_IMAGES.kost)\n  return images[index % images.length]\n}\n\nexport const getAvatarImage = (index: number = 0): string => {\n  const avatars = Object.values(UNSPLASH_IMAGES.avatars)\n  return avatars[index % avatars.length]\n}\n\nexport const getRandomKostImages = (count: number = 3): string[] => {\n  const images = Object.values(UNSPLASH_IMAGES.kost)\n  const shuffled = [...images].sort(() => 0.5 - Math.random())\n  return shuffled.slice(0, count)\n}\n\n// Default fallback image\nexport const DEFAULT_KOST_IMAGE = UNSPLASH_IMAGES.kost.room1\n\n// Image optimization parameters\nexport const IMAGE_PARAMS = {\n  quality: 80,\n  format: 'webp',\n  sizes: {\n    thumbnail: 'w=300&h=200',\n    card: 'w=400&h=300', \n    preview: 'w=800&h=600',\n    hero: 'w=1200&h=800',\n    og: 'w=1200&h=630',\n    avatar: 'w=100&h=100'\n  }\n} as const\n\n// Function to build optimized image URL\nexport const buildImageUrl = (baseUrl: string, size: keyof typeof IMAGE_PARAMS.sizes): string => {\n  const params = IMAGE_PARAMS.sizes[size]\n  const separator = baseUrl.includes('?') ? '&' : '?'\n  return `${baseUrl}${separator}${params}&fit=crop&crop=center&q=${IMAGE_PARAMS.quality}`\n}\n\n// Preload critical images\nexport const PRELOAD_IMAGES = [\n  UNSPLASH_IMAGES.kost.room1,\n  UNSPLASH_IMAGES.kost.room2,\n  UNSPLASH_IMAGES.kost.room3,\n] as const\n"], "names": [], "mappings": "AAAA,kCAAkC;AAClC,uEAAuE;;;;;;;;;;;AAEhE,MAAM,kBAAkB;IAC7B,wDAAwD;IACxD,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QAEP,wBAAwB;QACxB,WAAW;QACX,WAAW;QACX,WAAW;IACb;IAEA,iDAAiD;IACjD,SAAS;QACP,OAAO;QACP,SAAS;QACT,OAAO;QACP,SAAS;QACT,OAAO;QACP,SAAS;IACX;IAEA,uCAAuC;IACvC,IAAI;QACF,MAAM;QACN,UAAU;QACV,OAAO;QACP,SAAS;IACX;IAEA,wCAAwC;IACxC,WAAW;QACT,SAAS;QACT,SAAS;QACT,OAAO;QACP,UAAU;IACZ;IAEA,kBAAkB;IAClB,YAAY;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,SAAS;IACX;AACF;AAGO,MAAM,eAAe,CAAC,QAAgB,CAAC;IAC5C,MAAM,SAAS,OAAO,MAAM,CAAC,gBAAgB,IAAI;IACjD,OAAO,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;AACtC;AAEO,MAAM,iBAAiB,CAAC,QAAgB,CAAC;IAC9C,MAAM,UAAU,OAAO,MAAM,CAAC,gBAAgB,OAAO;IACrD,OAAO,OAAO,CAAC,QAAQ,QAAQ,MAAM,CAAC;AACxC;AAEO,MAAM,sBAAsB,CAAC,QAAgB,CAAC;IACnD,MAAM,SAAS,OAAO,MAAM,CAAC,gBAAgB,IAAI;IACjD,MAAM,WAAW;WAAI;KAAO,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IACzD,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAGO,MAAM,qBAAqB,gBAAgB,IAAI,CAAC,KAAK;AAGrD,MAAM,eAAe;IAC1B,SAAS;IACT,QAAQ;IACR,OAAO;QACL,WAAW;QACX,MAAM;QACN,SAAS;QACT,MAAM;QACN,IAAI;QACJ,QAAQ;IACV;AACF;AAGO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,MAAM,SAAS,aAAa,KAAK,CAAC,KAAK;IACvC,MAAM,YAAY,QAAQ,QAAQ,CAAC,OAAO,MAAM;IAChD,OAAO,GAAG,UAAU,YAAY,OAAO,wBAAwB,EAAE,aAAa,OAAO,EAAE;AACzF;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB,IAAI,CAAC,KAAK;IAC1B,gBAAgB,IAAI,CAAC,KAAK;IAC1B,gBAAgB,IAAI,CAAC,KAAK;CAC3B", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport { Navigation } from \"@/components/navigation\";\nimport { Footer } from \"@/components/footer\";\nimport { ErrorBoundary } from \"@/components/error-boundary\";\nimport { UNSPLASH_IMAGES } from \"@/lib/images\";\nimport \"./globals.css\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"KostHub - Platform Pencarian Kost Inovatif\",\n  description: \"Temukan kost impian Anda dengan mudah. Platform pencarian kost dengan preview dinamis dan fitur perbandingan interaktif untuk membantu Anda menemukan tempat tinggal yang sempurna.\",\n  keywords: [\"kost\", \"sewa kamar\", \"tempat tinggal\", \"boarding house\", \"pencarian kost\", \"kost murah\"],\n  authors: [{ name: \"KostHub Team\" }],\n  creator: \"KostHub\",\n  publisher: \"KostHub\",\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  metadataBase: new URL('https://kosthub.com'),\n  alternates: {\n    canonical: '/',\n  },\n  openGraph: {\n    title: \"KostHub - Platform Pencarian Kost Inovatif\",\n    description: \"Temukan kost impian Anda dengan mudah. Platform pencarian kost dengan preview dinamis dan fitur perbandingan interaktif.\",\n    url: 'https://kosthub.com',\n    siteName: 'KostHub',\n    images: [\n      {\n        url: UNSPLASH_IMAGES.og.main,\n        width: 1200,\n        height: 630,\n        alt: 'KostHub - Platform Pencarian Kost',\n      },\n    ],\n    locale: 'id_ID',\n    type: 'website',\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: \"KostHub - Platform Pencarian Kost Inovatif\",\n    description: \"Temukan kost impian Anda dengan mudah. Platform pencarian kost dengan preview dinamis dan fitur perbandingan interaktif.\",\n    images: [UNSPLASH_IMAGES.og.main],\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      'max-video-preview': -1,\n      'max-image-preview': 'large',\n      'max-snippet': -1,\n    },\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"id\" className=\"scroll-smooth\">\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-background font-sans`}\n      >\n        <div className=\"relative flex min-h-screen flex-col\">\n          <ErrorBoundary>\n            <Navigation />\n            <main className=\"flex-1\">\n              {children}\n            </main>\n            <Footer />\n          </ErrorBoundary>\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;;;;;;;;;AAaO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAQ;QAAc;QAAkB;QAAkB;QAAkB;KAAa;IACpG,SAAS;QAAC;YAAE,MAAM;QAAe;KAAE;IACnC,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,cAAc,IAAI,IAAI;IACtB,YAAY;QACV,WAAW;IACb;IACA,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;YACN;gBACE,KAAK,6GAAA,CAAA,kBAAe,CAAC,EAAE,CAAC,IAAI;gBAC5B,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC,6GAAA,CAAA,kBAAe,CAAC,EAAE,CAAC,IAAI;SAAC;IACnC;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,6WAAC;QAAK,MAAK;QAAK,WAAU;kBACxB,cAAA,6WAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,iDAAiD,CAAC;sBAEzG,cAAA,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,gIAAA,CAAA,gBAAa;;sCACZ,6WAAC,yHAAA,CAAA,aAAU;;;;;sCACX,6WAAC;4BAAK,WAAU;sCACb;;;;;;sCAEH,6WAAC,qHAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnB", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/.pnpm/next%4015.4.2_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,uMACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}