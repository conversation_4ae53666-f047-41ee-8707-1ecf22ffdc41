"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { STORAGE_KEYS } from "./constants"

// Hook for local storage
export function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue
    }
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      setStoredValue(valueToStore)
      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore))
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error)
    }
  }, [key, storedValue])

  return [storedValue, setValue] as const
}

// Hook for favorites management
export function useFavorites() {
  const [favorites, setFavorites] = useLocalStorage<string[]>(STORAGE_KEYS.favorites, [])

  const addToFavorites = useCallback((kostId: string) => {
    setFavorites(prev => [...prev.filter(id => id !== kostId), kostId])
  }, [setFavorites])

  const removeFromFavorites = useCallback((kostId: string) => {
    setFavorites(prev => prev.filter(id => id !== kostId))
  }, [setFavorites])

  const toggleFavorite = useCallback((kostId: string) => {
    setFavorites(prev => 
      prev.includes(kostId) 
        ? prev.filter(id => id !== kostId)
        : [...prev, kostId]
    )
  }, [setFavorites])

  const isFavorite = useCallback((kostId: string) => {
    return favorites.includes(kostId)
  }, [favorites])

  return {
    favorites,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    isFavorite
  }
}

// Hook for comparison management
export function useComparison() {
  const [comparison, setComparison] = useLocalStorage<string[]>(STORAGE_KEYS.comparison, [])

  const addToComparison = useCallback((kostId: string) => {
    setComparison(prev => {
      if (prev.includes(kostId)) return prev
      if (prev.length >= 3) {
        // Replace the first item if already at max
        return [kostId, ...prev.slice(1)]
      }
      return [...prev, kostId]
    })
  }, [setComparison])

  const removeFromComparison = useCallback((kostId: string) => {
    setComparison(prev => prev.filter(id => id !== kostId))
  }, [setComparison])

  const toggleComparison = useCallback((kostId: string) => {
    setComparison(prev => 
      prev.includes(kostId) 
        ? prev.filter(id => id !== kostId)
        : prev.length < 3 
          ? [...prev, kostId]
          : [kostId, ...prev.slice(1)]
    )
  }, [setComparison])

  const isInComparison = useCallback((kostId: string) => {
    return comparison.includes(kostId)
  }, [comparison])

  const clearComparison = useCallback(() => {
    setComparison([])
  }, [setComparison])

  return {
    comparison,
    addToComparison,
    removeFromComparison,
    toggleComparison,
    isInComparison,
    clearComparison,
    count: comparison.length
  }
}

// Hook for responsive design
export function useMediaQuery(query: string) {
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    if (typeof window === "undefined") return

    const media = window.matchMedia(query)
    setMatches(media.matches)

    const listener = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    media.addEventListener("change", listener)
    return () => media.removeEventListener("change", listener)
  }, [query])

  return matches
}

// Hook for debounced value
export function useDebounce<T>(value: T, delay: number) {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Hook for search functionality
export function useSearch<T>(
  items: T[],
  searchTerm: string,
  searchFields: (keyof T)[]
) {
  const filteredItems = useMemo(() => {
    if (!searchTerm.trim()) return items

    const lowercaseSearchTerm = searchTerm.toLowerCase()
    
    return items.filter(item =>
      searchFields.some(field => {
        const fieldValue = item[field]
        if (typeof fieldValue === "string") {
          return fieldValue.toLowerCase().includes(lowercaseSearchTerm)
        }
        if (Array.isArray(fieldValue)) {
          return fieldValue.some(val => 
            typeof val === "string" && val.toLowerCase().includes(lowercaseSearchTerm)
          )
        }
        return false
      })
    )
  }, [items, searchTerm, searchFields])

  return filteredItems
}

// Hook for pagination
export function usePagination<T>(items: T[], itemsPerPage: number) {
  const [currentPage, setCurrentPage] = useState(1)

  const totalPages = Math.ceil(items.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentItems = items.slice(startIndex, endIndex)

  const goToPage = useCallback((page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)))
  }, [totalPages])

  const goToNextPage = useCallback(() => {
    goToPage(currentPage + 1)
  }, [currentPage, goToPage])

  const goToPreviousPage = useCallback(() => {
    goToPage(currentPage - 1)
  }, [currentPage, goToPage])

  // Reset to first page when items change
  useEffect(() => {
    setCurrentPage(1)
  }, [items.length])

  return {
    currentPage,
    totalPages,
    currentItems,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1
  }
}

// Hook for intersection observer (for lazy loading)
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options?: IntersectionObserverInit
) {
  const [isIntersecting, setIsIntersecting] = useState(false)

  useEffect(() => {
    if (!elementRef.current) return

    const observer = new IntersectionObserver(
      ([entry]) => setIsIntersecting(entry.isIntersecting),
      options
    )

    observer.observe(elementRef.current)

    return () => observer.disconnect()
  }, [elementRef, options])

  return isIntersecting
}
