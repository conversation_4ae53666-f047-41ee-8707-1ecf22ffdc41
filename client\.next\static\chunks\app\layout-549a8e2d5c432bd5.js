(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{298:(e,a,s)=>{"use strict";s.d(a,{Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var t=s(2273);s(5461);var r=s(1415);function n(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...s})}function i(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...s})}function l(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",a),...s})}},421:(e,a,s)=>{"use strict";s.d(a,{CG:()=>l,Fm:()=>h,cj:()=>o,h:()=>m,qp:()=>x});var t=s(2273);s(5461);var r=s(9472),n=s(9889),i=s(1415);function o(e){let{...a}=e;return(0,t.jsx)(r.bL,{"data-slot":"sheet",...a})}function l(e){let{...a}=e;return(0,t.jsx)(r.l9,{"data-slot":"sheet-trigger",...a})}function c(e){let{...a}=e;return(0,t.jsx)(r.ZL,{"data-slot":"sheet-portal",...a})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(r.hJ,{"data-slot":"sheet-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...s})}function m(e){let{className:a,children:s,side:o="right",...l}=e;return(0,t.jsxs)(c,{children:[(0,t.jsx)(d,{}),(0,t.jsxs)(r.UC,{"data-slot":"sheet-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===o&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===o&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===o&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===o&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",a),...l,children:[s,(0,t.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,t.jsx)(n.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"sheet-header",className:(0,i.cn)("flex flex-col gap-1.5 p-4",a),...s})}function x(e){let{className:a,...s}=e;return(0,t.jsx)(r.hE,{"data-slot":"sheet-title",className:(0,i.cn)("text-foreground font-semibold",a),...s})}},1415:(e,a,s)=>{"use strict";s.d(a,{cn:()=>n});var t=s(9898),r=s(5422);function n(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,r.QP)((0,t.$)(a))}},2154:(e,a,s)=>{"use strict";s.d(a,{Footer:()=>j});var t=s(2273),r=s(1702),n=s.n(r),i=s(7320),o=s(3293),l=s(7279),c=s(5809),d=s(8973),m=s(7900),h=s(7164),x=s(8684),u=s(4529),f=s(1101);let p={company:[{name:"Tentang Kami",href:"/about"},{name:"Karir",href:"/careers"},{name:"Blog",href:"/blog"},{name:"Press",href:"/press"}],support:[{name:"Pusat Bantuan",href:"/help"},{name:"Kontak",href:"/contact"},{name:"FAQ",href:"/faq"},{name:"Panduan",href:"/guide"}],legal:[{name:"Syarat & Ketentuan",href:"/terms"},{name:"Kebijakan Privasi",href:"/privacy"},{name:"Kebijakan Cookie",href:"/cookies"},{name:"Disclaimer",href:"/disclaimer"}],services:[{name:"Cari Kost",href:"/listings"},{name:"Daftarkan Kost",href:"/register-kost"},{name:"Verifikasi Kost",href:"/verification"},{name:"Premium",href:"/premium"}]},g=[{name:"Facebook",icon:l.A,href:"#"},{name:"Twitter",icon:c.A,href:"#"},{name:"Instagram",icon:d.A,href:"#"},{name:"YouTube",icon:m.A,href:"#"}];function j(){return(0,t.jsx)("footer",{className:"bg-muted/30 border-t",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-4",children:[(0,t.jsxs)(n(),{href:"/",className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-8 w-8 text-primary"}),(0,t.jsx)("span",{className:"text-2xl font-bold text-primary",children:"KostHub"})]}),(0,t.jsx)("p",{className:"text-muted-foreground leading-relaxed max-w-md",children:"Platform pencarian kost terdepan dengan fitur preview dinamis dan perbandingan interaktif. Temukan tempat tinggal ideal Anda dengan mudah dan aman."}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"<EMAIL>"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"+62 21 1234 5678"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Jakarta, Indonesia"})]})]}),(0,t.jsx)("div",{className:"flex space-x-2",children:g.map(e=>(0,t.jsx)(i.$,{variant:"ghost",size:"sm",className:"h-9 w-9 p-0",asChild:!0,children:(0,t.jsx)(n(),{href:e.href,"aria-label":e.name,children:(0,t.jsx)(e.icon,{className:"h-4 w-4"})})},e.name))})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold",children:"Perusahaan"}),(0,t.jsx)("ul",{className:"space-y-2",children:p.company.map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:e.href,className:"text-sm text-muted-foreground hover:text-primary transition-colors",children:e.name})},e.name))})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold",children:"Layanan"}),(0,t.jsx)("ul",{className:"space-y-2",children:p.services.map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:e.href,className:"text-sm text-muted-foreground hover:text-primary transition-colors",children:e.name})},e.name))})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold",children:"Dukungan"}),(0,t.jsx)("ul",{className:"space-y-2",children:p.support.map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:e.href,className:"text-sm text-muted-foreground hover:text-primary transition-colors",children:e.name})},e.name))}),(0,t.jsxs)("div",{className:"pt-2",children:[(0,t.jsx)("h4",{className:"font-medium text-sm mb-2",children:"Legal"}),(0,t.jsx)("ul",{className:"space-y-1",children:p.legal.map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:e.href,className:"text-xs text-muted-foreground hover:text-primary transition-colors",children:e.name})},e.name))})]})]})]}),(0,t.jsx)(o.w,{className:"my-8"}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-4",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"\xa9 2024 KostHub. Semua hak dilindungi."}),(0,t.jsx)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:(0,t.jsx)("span",{children:"Dibuat dengan ❤️ di Indonesia"})})]})]})})}},2189:(e,a,s)=>{"use strict";s.d(a,{Navigation:()=>j});var t=s(2273),r=s(5461),n=s(1702),i=s.n(n),o=s(7320),l=s(421),c=s(9269),d=s(4991),m=s(6309),h=s(3155),x=s(4529),u=s(7164),f=s(560),p=s(5448);let g=[{name:"Beranda",href:"/",icon:c.A},{name:"Cari Kost",href:"/listings",icon:d.A},{name:"Favorit",href:"/favorites",icon:m.A},{name:"Tentang",href:"/about",icon:h.A},{name:"Kontak",href:"/contact",icon:x.A}];function j(){let[e,a]=(0,r.useState)(!1);return(0,t.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,t.jsx)("div",{className:"container mx-auto px-4",children:(0,t.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,t.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"h-8 w-8 text-primary"}),(0,t.jsx)("span",{className:"text-2xl font-bold text-primary",children:"KostHub"})]}),(0,t.jsx)("nav",{className:"hidden md:flex items-center space-x-6",children:g.map(e=>(0,t.jsxs)(i(),{href:e.href,className:"flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-primary transition-colors",children:[(0,t.jsx)(e.icon,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.name})]},e.name))}),(0,t.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,t.jsx)(o.$,{variant:"ghost",size:"sm",children:"Masuk"}),(0,t.jsx)(o.$,{size:"sm",children:"Daftar"})]}),(0,t.jsxs)(l.cj,{open:e,onOpenChange:a,children:[(0,t.jsx)(l.CG,{asChild:!0,children:(0,t.jsx)(o.$,{variant:"ghost",size:"sm",className:"md:hidden",children:(0,t.jsx)(f.A,{className:"h-5 w-5"})})}),(0,t.jsx)(l.h,{side:"right",className:"w-80",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-4 mt-8",children:[g.map(e=>(0,t.jsxs)(i(),{href:e.href,onClick:()=>a(!1),className:"flex items-center space-x-3 text-lg font-medium text-muted-foreground hover:text-primary transition-colors p-2 rounded-lg hover:bg-muted",children:[(0,t.jsx)(e.icon,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:e.name})]},e.name)),(0,t.jsxs)("div",{className:"pt-4 border-t space-y-2",children:[(0,t.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Masuk"]}),(0,t.jsx)(o.$,{className:"w-full justify-start",children:"Daftar"})]})]})})]})]})})})}},3293:(e,a,s)=>{"use strict";s.d(a,{w:()=>i});var t=s(2273);s(5461);var r=s(4046),n=s(1415);function i(e){let{className:a,orientation:s="horizontal",decorative:i=!0,...o}=e;return(0,t.jsx)(r.b,{"data-slot":"separator",decorative:i,orientation:s,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...o})}},4362:(e,a,s)=>{"use strict";s.d(a,{ErrorBoundary:()=>h});var t=s(2273),r=s(5461),n=s(7320),i=s(298),o=s(5516),l=s(8347),c=s(9269);class d extends r.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,a){console.error("Error caught by boundary:",e,a)}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return(0,t.jsx)(e,{error:this.state.error,resetError:this.resetError})}return(0,t.jsx)(m,{error:this.state.error,resetError:this.resetError})}return this.props.children}constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}}function m(e){let{error:a,resetError:s}=e;return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4 bg-background",children:(0,t.jsxs)(i.Zp,{className:"w-full max-w-md",children:[(0,t.jsxs)(i.aR,{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(o.A,{className:"h-8 w-8 text-destructive"})}),(0,t.jsx)(i.ZB,{className:"text-xl",children:"Oops! Terjadi Kesalahan"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-muted-foreground text-center",children:"Maaf, terjadi kesalahan yang tidak terduga. Silakan coba lagi atau kembali ke halaman utama."}),!1,(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[(0,t.jsxs)(n.$,{onClick:s,className:"flex-1",children:[(0,t.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Coba Lagi"]}),(0,t.jsxs)(n.$,{variant:"outline",onClick:()=>window.location.href="/",className:"flex-1",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Ke Beranda"]})]})]})]})})}let h=d},7320:(e,a,s)=>{"use strict";s.d(a,{$:()=>l});var t=s(2273);s(5461);var r=s(3330),n=s(8092),i=s(1415);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:a,variant:s,size:n,asChild:l=!1,...c}=e,d=l?r.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:s,size:n,className:a})),...c})}},8346:()=>{},9413:(e,a,s)=>{Promise.resolve().then(s.t.bind(s,8346,23)),Promise.resolve().then(s.bind(s,4362)),Promise.resolve().then(s.bind(s,2154)),Promise.resolve().then(s.bind(s,2189)),Promise.resolve().then(s.t.bind(s,2031,23)),Promise.resolve().then(s.t.bind(s,9013,23))}},e=>{e.O(0,[431,335,482,760,769,970,804,358],()=>e(e.s=9413)),_N_E=e.O()}]);