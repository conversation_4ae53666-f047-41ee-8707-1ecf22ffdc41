1:"$Sreact.fragment"
2:I[4362,["335","static/chunks/335-0eb22bb366a05850.js","482","static/chunks/482-2e4dde37b949c5ab.js","402","static/chunks/402-6543dc6c66335d17.js","50","static/chunks/50-90ade593014657a6.js","177","static/chunks/app/layout-e2668fb46bb9a83e.js"],"ErrorBoundary"]
3:I[2189,["335","static/chunks/335-0eb22bb366a05850.js","482","static/chunks/482-2e4dde37b949c5ab.js","402","static/chunks/402-6543dc6c66335d17.js","50","static/chunks/50-90ade593014657a6.js","177","static/chunks/app/layout-e2668fb46bb9a83e.js"],"Navigation"]
4:I[1423,[],""]
5:I[9235,[],""]
6:I[2154,["335","static/chunks/335-0eb22bb366a05850.js","482","static/chunks/482-2e4dde37b949c5ab.js","402","static/chunks/402-6543dc6c66335d17.js","50","static/chunks/50-90ade593014657a6.js","177","static/chunks/app/layout-e2668fb46bb9a83e.js"],"Footer"]
7:I[3978,[],"ClientPageRoot"]
8:I[8877,["335","static/chunks/335-0eb22bb366a05850.js","482","static/chunks/482-2e4dde37b949c5ab.js","739","static/chunks/739-4b7d42cbafeaf8ce.js","644","static/chunks/644-386975e2ab9d15b3.js","402","static/chunks/402-6543dc6c66335d17.js","256","static/chunks/256-a617895d74914822.js","450","static/chunks/450-a04173d12404a99f.js","214","static/chunks/214-bfcaa1d692406af5.js","305","static/chunks/305-32119ee13893780e.js","172","static/chunks/172-aaafa7a5854c390f.js","914","static/chunks/914-bba2bd7803b8a516.js","197","static/chunks/app/listings/page-ea3af22c0ea2da14.js"],"default"]
b:I[8445,[],"OutletBoundary"]
d:I[7867,[],"AsyncMetadataOutlet"]
f:I[8445,[],"ViewportBoundary"]
11:I[8445,[],"MetadataBoundary"]
12:"$Sreact.suspense"
14:I[7173,[],""]
:HL["/_next/static/css/3a62c306c7effae9.css","style"]
0:{"P":null,"b":"TbHcWEynwdatGJqYR6G0W","p":"","c":["","listings"],"i":false,"f":[[["",{"children":["listings",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/3a62c306c7effae9.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"id","className":"scroll-smooth","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased min-h-screen bg-background font-sans","children":["$","div",null,{"className":"relative flex min-h-screen flex-col","children":["$","$L2",null,{"children":[["$","$L3",null,{}],["$","main",null,{"className":"flex-1","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L6",null,{}]]}]}]}]}]]}],{"children":["listings",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L7",null,{"Component":"$8","searchParams":{},"params":{},"promises":["$@9","$@a"]}],null,["$","$Lb",null,{"children":["$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$Lf",null,{"children":"$L10"}],null],["$","$L11",null,{"children":["$","div",null,{"hidden":true,"children":["$","$12",null,{"fallback":null,"children":"$L13"}]}]}]]}],false]],"m":"$undefined","G":["$14",[]],"s":false,"S":true}
9:{}
a:"$0:f:0:1:2:children:2:children:1:props:children:0:props:params"
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
15:I[9283,[],"IconMark"]
e:{"metadata":[["$","title","0",{"children":"Pencarian Kost - KostHub"}],["$","meta","1",{"name":"description","content":"Temukan kost terbaik dengan fitur pencarian canggih, filter detail, dan perbandingan interaktif. Ribuan pilihan kost terverifikasi di seluruh Indonesia."}],["$","meta","2",{"name":"author","content":"KostHub Team"}],["$","meta","3",{"name":"keywords","content":"pencarian kost,daftar kost,kost terdekat,sewa kamar,boarding house"}],["$","meta","4",{"name":"creator","content":"KostHub"}],["$","meta","5",{"name":"publisher","content":"KostHub"}],["$","meta","6",{"name":"robots","content":"index, follow"}],["$","meta","7",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","link","8",{"rel":"canonical","href":"https://kosthub.com"}],["$","meta","9",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","10",{"property":"og:title","content":"Pencarian Kost - KostHub"}],["$","meta","11",{"property":"og:description","content":"Temukan kost terbaik dengan fitur pencarian canggih dan filter detail."}],["$","meta","12",{"property":"og:image","content":"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=1200&h=630&fit=crop&crop=center"}],["$","meta","13",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","14",{"name":"twitter:title","content":"KostHub - Platform Pencarian Kost Inovatif"}],["$","meta","15",{"name":"twitter:description","content":"Temukan kost impian Anda dengan mudah. Platform pencarian kost dengan preview dinamis dan fitur perbandingan interaktif."}],["$","meta","16",{"name":"twitter:image","content":"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=1200&h=630&fit=crop&crop=center"}],["$","link","17",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L15","18",{}]],"error":null,"digest":"$undefined"}
13:"$e:metadata"
