"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { Kost<PERSON>ard, KostData } from "@/components/kost-card"
import { SearchBar, SearchFilters } from "@/components/search-bar"
import { KostPreviewDialog } from "@/components/kost-preview-dialog"
import { ComparisonDialog } from "@/components/comparison-dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  Filter,
  Grid3X3,
  List,
  MapPin,
  SlidersHorizontal,
  ArrowUpDown
} from "lucide-react"

// Mock data untuk listings
const mockKosts: KostData[] = [
  {
    id: "1",
    title: "Kost Melati Residence",
    location: "Kemang, Jakarta Selatan",
    price: 2500000,
    rating: 4.8,
    reviewCount: 124,
    images: ["/placeholder-kost.jpg"],
    facilities: ["WiFi", "Parkir", "<PERSON>pur", "<PERSON>rik", "Air", "Keamanan"],
    type: "putri",
    available: 3,
    description: "Kost nyaman dan strategis di kawasan Kemang dengan fasilitas lengkap dan keamanan 24 jam.",
    isWishlisted: false
  },
  {
    id: "2", 
    title: "Griya Mahasiswa Bandung",
    location: "Dago, Bandung",
    price: 1800000,
    rating: 4.6,
    reviewCount: 89,
    images: ["/placeholder-kost.jpg"],
    facilities: ["WiFi", "Dapur", "Listrik", "Air", "Ruang Tamu"],
    type: "putra",
    available: 5,
    description: "Kost khusus mahasiswa dengan suasana yang kondusif untuk belajar dan dekat dengan kampus.",
    isWishlisted: true
  },
  {
    id: "3",
    title: "Kost Harmoni Yogya",
    location: "Malioboro, Yogyakarta", 
    price: 1500000,
    rating: 4.7,
    reviewCount: 156,
    images: ["/placeholder-kost.jpg"],
    facilities: ["WiFi", "Parkir", "Listrik", "Air", "Keamanan", "AC"],
    type: "campur",
    available: 2,
    description: "Kost dengan lokasi strategis di jantung kota Yogyakarta, dekat dengan berbagai fasilitas umum.",
    isWishlisted: false
  },
  {
    id: "4",
    title: "Kost Mawar Indah",
    location: "Menteng, Jakarta Pusat",
    price: 3200000,
    rating: 4.9,
    reviewCount: 78,
    images: ["/placeholder-kost.jpg"],
    facilities: ["WiFi", "Parkir", "Dapur", "Listrik", "Air", "Keamanan", "AC", "Kasur"],
    type: "putri",
    available: 1,
    description: "Kost premium dengan fasilitas mewah di kawasan elite Jakarta Pusat.",
    isWishlisted: false
  },
  {
    id: "5",
    title: "Kost Sederhana Malang",
    location: "Dinoyo, Malang",
    price: 1200000,
    rating: 4.3,
    reviewCount: 45,
    images: ["/placeholder-kost.jpg"],
    facilities: ["WiFi", "Listrik", "Air"],
    type: "putra",
    available: 8,
    description: "Kost ekonomis untuk mahasiswa dengan fasilitas dasar yang memadai.",
    isWishlisted: false
  },
  {
    id: "6",
    title: "Kost Anggrek Surabaya",
    location: "Gubeng, Surabaya",
    price: 2100000,
    rating: 4.5,
    reviewCount: 92,
    images: ["/placeholder-kost.jpg"],
    facilities: ["WiFi", "Parkir", "Dapur", "Listrik", "Air", "Ruang Tamu"],
    type: "campur",
    available: 4,
    description: "Kost nyaman di pusat kota Surabaya dengan akses mudah ke berbagai tempat.",
    isWishlisted: false
  }
]

export default function ListingsPage() {
  const searchParams = useSearchParams()
  const [kosts, setKosts] = useState<KostData[]>([])
  const [filteredKosts, setFilteredKosts] = useState<KostData[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedKost, setSelectedKost] = useState<KostData | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [comparisonKosts, setComparisonKosts] = useState<KostData[]>([])
  const [isComparisonOpen, setIsComparisonOpen] = useState(false)
  const [wishlistedKosts, setWishlistedKosts] = useState<string[]>(["2"])
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [currentPage, setCurrentPage] = useState(1)
  const [sortBy, setSortBy] = useState("relevance")
  
  const itemsPerPage = 6

  // Initialize data
  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setKosts(mockKosts)
      setFilteredKosts(mockKosts)
      setLoading(false)
    }, 1000)
  }, [])

  // Handle search from URL params
  useEffect(() => {
    const location = searchParams.get('location')
    const type = searchParams.get('type')
    const query = searchParams.get('q')
    
    if (location || type || query) {
      // Apply initial filters based on URL params
      handleSearch(query || "", {
        location: location || "",
        type: (type as any) || "semua",
        priceRange: [500000, 5000000],
        facilities: [],
        sortBy: "relevance"
      })
    }
  }, [searchParams])

  const handleSearch = (query: string, filters: SearchFilters) => {
    let filtered = [...kosts]

    // Apply text search
    if (query) {
      filtered = filtered.filter(kost => 
        kost.title.toLowerCase().includes(query.toLowerCase()) ||
        kost.location.toLowerCase().includes(query.toLowerCase()) ||
        kost.facilities.some(f => f.toLowerCase().includes(query.toLowerCase()))
      )
    }

    // Apply location filter
    if (filters.location) {
      filtered = filtered.filter(kost => 
        kost.location.toLowerCase().includes(filters.location.toLowerCase())
      )
    }

    // Apply type filter
    if (filters.type !== "semua") {
      filtered = filtered.filter(kost => kost.type === filters.type)
    }

    // Apply price range filter
    filtered = filtered.filter(kost => 
      kost.price >= filters.priceRange[0] && kost.price <= filters.priceRange[1]
    )

    // Apply facilities filter
    if (filters.facilities.length > 0) {
      filtered = filtered.filter(kost =>
        filters.facilities.every(facility =>
          kost.facilities.some(f => f.toLowerCase() === facility.toLowerCase())
        )
      )
    }

    // Apply sorting
    switch (filters.sortBy) {
      case "price-low":
        filtered.sort((a, b) => a.price - b.price)
        break
      case "price-high":
        filtered.sort((a, b) => b.price - a.price)
        break
      case "rating":
        filtered.sort((a, b) => b.rating - a.rating)
        break
      case "newest":
        // For demo, just reverse the order
        filtered.reverse()
        break
      default:
        // Keep original order for relevance
        break
    }

    setFilteredKosts(filtered)
    setCurrentPage(1)
  }

  const handlePreview = (kost: KostData) => {
    setSelectedKost(kost)
    setIsPreviewOpen(true)
  }

  const handleWishlist = (kostId: string) => {
    setWishlistedKosts(prev => 
      prev.includes(kostId) 
        ? prev.filter(id => id !== kostId)
        : [...prev, kostId]
    )
  }

  const handleCompare = (kostId: string) => {
    const kost = kosts.find(k => k.id === kostId)
    if (!kost) return

    setComparisonKosts(prev => {
      const isAlreadyComparing = prev.some(k => k.id === kostId)
      if (isAlreadyComparing) {
        return prev.filter(k => k.id !== kostId)
      } else if (prev.length < 3) {
        return [...prev, kost]
      } else {
        return [kost, ...prev.slice(1)]
      }
    })
  }

  const removeFromComparison = (kostId: string) => {
    setComparisonKosts(prev => prev.filter(k => k.id !== kostId))
  }

  // Pagination
  const totalPages = Math.ceil(filteredKosts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentKosts = filteredKosts.slice(startIndex, endIndex)

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="space-y-6">
            <Skeleton className="h-12 w-full max-w-2xl mx-auto" />
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="space-y-4">
                  <Skeleton className="aspect-[4/3] w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Pencarian Kost</h1>
          <SearchBar onSearch={handleSearch} />
        </div>

        {/* Results Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <p className="text-muted-foreground">
              Menampilkan {filteredKosts.length} kost
            </p>
            {filteredKosts.length !== kosts.length && (
              <Badge variant="secondary">
                Hasil pencarian
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            {/* View Mode Toggle */}
            <div className="flex items-center border rounded-lg p-1">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="h-8 w-8 p-0"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="h-8 w-8 p-0"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>

            {/* Sort Dropdown */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-48">
                <ArrowUpDown className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="relevance">Paling Relevan</SelectItem>
                <SelectItem value="price-low">Harga Terendah</SelectItem>
                <SelectItem value="price-high">Harga Tertinggi</SelectItem>
                <SelectItem value="rating">Rating Tertinggi</SelectItem>
                <SelectItem value="newest">Terbaru</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Results Grid */}
        {filteredKosts.length === 0 ? (
          <div className="text-center py-12">
            <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Tidak ada kost ditemukan</h3>
            <p className="text-muted-foreground">
              Coba ubah filter pencarian atau kata kunci Anda
            </p>
          </div>
        ) : (
          <>
            <div className={`grid gap-6 mb-8 ${
              viewMode === "grid" 
                ? "md:grid-cols-2 lg:grid-cols-3" 
                : "grid-cols-1"
            }`}>
              {currentKosts.map((kost) => (
                <KostCard
                  key={kost.id}
                  kost={{
                    ...kost,
                    isWishlisted: wishlistedKosts.includes(kost.id)
                  }}
                  onPreview={handlePreview}
                  onWishlist={handleWishlist}
                  onCompare={handleCompare}
                  isComparing={comparisonKosts.some(k => k.id === kost.id)}
                  className={viewMode === "list" ? "flex-row" : ""}
                />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Sebelumnya
                </Button>
                
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    onClick={() => setCurrentPage(page)}
                    className="w-10"
                  >
                    {page}
                  </Button>
                ))}
                
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Selanjutnya
                </Button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Comparison Floating Button */}
      {comparisonKosts.length > 0 && (
        <div className="fixed bottom-6 right-6 z-50">
          <Button
            onClick={() => setIsComparisonOpen(true)}
            className="rounded-full shadow-lg"
            size="lg"
          >
            Bandingkan ({comparisonKosts.length})
          </Button>
        </div>
      )}

      {/* Dialogs */}
      <KostPreviewDialog
        kost={selectedKost}
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        onWishlist={handleWishlist}
        onCompare={handleCompare}
        isComparing={selectedKost ? comparisonKosts.some(k => k.id === selectedKost.id) : false}
      />

      <ComparisonDialog
        kosts={comparisonKosts}
        isOpen={isComparisonOpen}
        onClose={() => setIsComparisonOpen(false)}
        onRemoveFromComparison={removeFromComparison}
      />
    </div>
  )
}
